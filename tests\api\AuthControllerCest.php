<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\components\JwtHelper;

class AuthControllerCest
{
    private $testUser;

    public function _before(ApiTester $I)
    {
        // Create test user
        $this->testUser = new User();
        $this->testUser->username = 'testuser';
        $this->testUser->email = '<EMAIL>';
        $this->testUser->setPassword('testpass123');
        $this->testUser->role = User::ROLE_USER;
        $this->testUser->status = User::STATUS_ACTIVE;
        $this->testUser->save();
    }

    public function testLoginSuccess(ApiTester $I)
    {
        $I->sendPOST('/api/auth/login', [
            'username' => 'testuser',
            'password' => 'testpass123'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.token');
        $I->seeResponseJsonMatchesJsonPath('$.data.user.username');
    }

    public function testLoginInvalidCredentials(ApiTester $I)
    {
        $I->sendPOST('/api/auth/login', [
            'username' => 'testuser',
            'password' => 'wrongpassword'
        ]);
        
        $I->seeResponseCodeIs(401);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid username or password'
            ]
        ]);
    }

    public function testLoginMissingFields(ApiTester $I)
    {
        $I->sendPOST('/api/auth/login', [
            'username' => 'testuser'
            // Missing password
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testLoginNonExistentUser(ApiTester $I)
    {
        $I->sendPOST('/api/auth/login', [
            'username' => 'nonexistent',
            'password' => 'password123'
        ]);
        
        $I->seeResponseCodeIs(401);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid username or password'
            ]
        ]);
    }

    public function testVerifyValidToken(ApiTester $I)
    {
        // Generate valid token
        $token = JwtHelper::generateToken($this->testUser);
        
        $I->sendPOST('/api/auth/verify', [
            'token' => $token
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'valid' => true
            ]
        ]);
    }

    public function testVerifyInvalidToken(ApiTester $I)
    {
        $I->sendPOST('/api/auth/verify', [
            'token' => 'invalid.jwt.token'
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'data' => [
                'valid' => false
            ]
        ]);
    }

    public function testVerifyMissingToken(ApiTester $I)
    {
        $I->sendPOST('/api/auth/verify', []);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testVerifyExpiredToken(ApiTester $I)
    {
        // This would require creating an expired token
        // For now, we'll test with an invalid token format
        $I->sendPOST('/api/auth/verify', [
            'token' => 'expired.token.here'
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'data' => [
                'valid' => false
            ]
        ]);
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test user
        if ($this->testUser && $this->testUser->id) {
            $this->testUser->delete();
        }
    }
}
