# API Quick Operations Documentation

## Overview

This document describes the fast warehouse operations API endpoints designed for high-performance inventory management. All operations are atomic and use database transactions to ensure data consistency.

## Authentication

All API endpoints (except authentication and public info) require JWT authentication:

```
Authorization: Bearer <jwt_token>
```

## Quick Income Operations

### 1. Scan Barcode for Income

**Endpoint:** `POST /api/income/scan-barcode`

**Description:** Process income by scanning product barcode

**Request Body:**
```json
{
  "barcode": "1234567890123",
  "quantity": 10.5,
  "price_per_unit": 25.99,
  "notes": "Delivery from supplier ABC"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "income": {
      "id": 123,
      "product_id": 45,
      "quantity": 10.5,
      "price_per_unit": 25.99,
      "total_amount": 272.895,
      "notes": "Delivery from supplier ABC",
      "created_at": "2025-06-03 12:30:45"
    },
    "product": {
      "id": 45,
      "name": "Product Name",
      "barcode": "1234567890123",
      "previous_stock": 15.0,
      "current_stock": 25.5,
      "unit_type": "piece"
    },
    "operation_time": 0.045
  }
}
```

### 2. Quick Entry with Auto-Creation

**Endpoint:** `POST /api/income/quick-entry`

**Description:** Quick income entry with automatic product creation

**Request Body:**
```json
{
  "product": {
    "name": "New Product",
    "barcode": "9876543210987",
    "unit_type": "kg",
    "category_id": 5
  },
  "quantity": 20.0,
  "price_per_unit": 15.50,
  "auto_create": true,
  "notes": "New product arrival"
}
```

### 3. Batch Income Processing

**Endpoint:** `POST /api/income/batch`

**Description:** Process multiple income records in one request

**Request Body:**
```json
{
  "items": [
    {
      "product_id": 1,
      "quantity": 10,
      "price_per_unit": 25.99,
      "notes": "Batch item 1"
    },
    {
      "product_id": 2,
      "quantity": 5,
      "price_per_unit": 45.00,
      "notes": "Batch item 2"
    }
  ]
}
```

### 4. Search Products for Income

**Endpoint:** `GET /api/income/search?q=search_term&limit=20`

**Description:** Search products for income operations with price history

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": 1,
        "name": "Product Name",
        "barcode": "1234567890123",
        "current_stock": 15.5,
        "unit_type": "piece",
        "last_income_price": 25.99,
        "avg_income_price": 24.50,
        "category": {
          "id": 1,
          "name": "Category Name"
        }
      }
    ],
    "count": 1,
    "query": "search_term",
    "operation_time": 0.025
  }
}
```

## Quick Sales Operations

### 1. Scan Barcode for Sale

**Endpoint:** `POST /api/sales/scan-barcode`

**Description:** Process sale by scanning product barcode

**Request Body:**
```json
{
  "barcode": "1234567890123",
  "quantity": 2,
  "price_per_unit": 35.99,
  "notes": "Customer sale"
}
```

**Response (201):**
```json
{
  "success": true,
  "data": {
    "sale": {
      "id": 456,
      "product_id": 45,
      "quantity": 2,
      "price_per_unit": 35.99,
      "total_amount": 71.98,
      "notes": "Customer sale",
      "created_at": "2025-06-03 12:35:20"
    },
    "product": {
      "id": 45,
      "name": "Product Name",
      "barcode": "1234567890123",
      "previous_stock": 25.5,
      "current_stock": 23.5,
      "unit_type": "piece"
    },
    "total_amount": 71.98,
    "operation_time": 0.038
  }
}
```

### 2. Quick Sale Entry

**Endpoint:** `POST /api/sales/quick-entry`

**Description:** Quick sale entry by product search

**Request Body:**
```json
{
  "product": {
    "id": 45
  },
  "quantity": 1,
  "price_per_unit": 35.99,
  "notes": "Quick sale"
}
```

### 3. Search Products for Sale

**Endpoint:** `GET /api/sales/search?q=search_term&limit=20`

**Description:** Search products available for sale (only products with stock)

### 4. Get Popular Products

**Endpoint:** `GET /api/sales/popular?limit=10&days=30`

**Description:** Get most sold products for quick access

**Response:**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": 1,
        "name": "Popular Product",
        "barcode": "1234567890123",
        "current_stock": 50,
        "price_per_unit": 25.99,
        "total_sold": 150
      }
    ],
    "count": 1,
    "period_days": 30,
    "operation_time": 0.020
  }
}
```

## Error Responses

### Common Error Codes

- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing JWT token)
- `404` - Not Found (product/resource not found)
- `500` - Internal Server Error

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Validation failed",
    "details": {
      "quantity": ["Quantity must be greater than 0"]
    }
  }
}
```

### Specific Error Cases

**Insufficient Stock:**
```json
{
  "success": false,
  "error": {
    "code": 400,
    "message": "Insufficient stock",
    "details": {
      "available_stock": 5,
      "requested_quantity": 10,
      "product_name": "Product Name"
    }
  }
}
```

**Product Not Found:**
```json
{
  "success": false,
  "error": {
    "code": 404,
    "message": "Product not found",
    "details": {
      "barcode": "1234567890123",
      "suggestion": "Use quick-entry endpoint to create new product"
    }
  }
}
```

## Performance Notes

- All operations are designed to complete in under 200ms
- Database transactions ensure atomicity
- Optimistic locking prevents race conditions
- Comprehensive audit logging for all operations
- Automatic stock validation and updates

## Rate Limiting

- Maximum 100 items per batch operation
- Search results limited to 50 items maximum
- API requests are logged for monitoring

## Audit Trail

All operations are automatically logged with:
- User information
- Timestamp
- Operation details
- Before/after values
- IP address and user agent
