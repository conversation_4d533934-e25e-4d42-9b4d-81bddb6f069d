<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\components\JwtHelper;

class ProductControllerCest
{
    private $authToken;
    private $testCategory;
    private $testProduct;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->save();

        // Create test product
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $this->testCategory->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 10;
        $this->testProduct->save();
    }

    public function testGetProductsWithAuth(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/products');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data');
    }

    public function testGetProductsWithoutAuth(ApiTester $I)
    {
        $I->sendGET('/api/products');
        
        $I->seeResponseCodeIs(401);
    }

    public function testGetSingleProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/products/' . $this->testProduct->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Test Product',
                'barcode' => '1234567890123'
            ]
        ]);
    }

    public function testGetNonExistentProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/products/99999');
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCreateProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/products', [
            'name' => 'New Test Product',
            'barcode' => '9876543210987',
            'category_id' => $this->testCategory->id,
            'unit_type' => Product::UNIT_KG,
            'price_per_unit' => 15.50,
            'current_stock' => 5,
            'description' => 'Test product description'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'New Test Product',
                'barcode' => '9876543210987',
                'unit_type' => Product::UNIT_KG
            ]
        ]);
    }

    public function testCreateProductWithInvalidData(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/products', [
            'name' => '', // Empty name
            'unit_type' => 'invalid_unit'
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCreateProductWithDuplicateBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/products', [
            'name' => 'Duplicate Barcode Product',
            'barcode' => '1234567890123', // Same as test product
            'category_id' => $this->testCategory->id,
            'unit_type' => Product::UNIT_PIECE,
            'price_per_unit' => 20.00
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testUpdateProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPUT('/api/products/' . $this->testProduct->id, [
            'name' => 'Updated Test Product',
            'price_per_unit' => 30.00,
            'description' => 'Updated description'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Updated Test Product',
                'price_per_unit' => 30.00
            ]
        ]);
    }

    public function testUpdateNonExistentProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPUT('/api/products/99999', [
            'name' => 'Updated Product'
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testDeleteProduct(ApiTester $I)
    {
        // Create a product to delete
        $productToDelete = new Product();
        $productToDelete->name = 'Product to Delete';
        $productToDelete->barcode = '5555555555555';
        $productToDelete->category_id = $this->testCategory->id;
        $productToDelete->unit_type = Product::UNIT_PIECE;
        $productToDelete->price_per_unit = 10.00;
        $productToDelete->save();

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendDELETE('/api/products/' . $productToDelete->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
    }

    public function testDeleteNonExistentProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendDELETE('/api/products/99999');
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testProductsWithPagination(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/products?page=1&per-page=5');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data');
    }

    public function testProductsWithSearch(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/products?search=Test');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testProduct && $this->testProduct->id) {
            $this->testProduct->delete();
        }
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
