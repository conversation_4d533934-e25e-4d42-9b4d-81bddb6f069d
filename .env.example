# Environment Configuration
YII_DEBUG=false
YII_ENV=prod

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production

# Company Information
COMPANY_NAME=Warehouse Management System
COMPANY_ADDRESS=Your Company Address Here

# Supabase Database Configuration (Option 1: Full URL)
# Get this from your Supabase project settings -> Database -> Connection string
# SUPABASE_DB_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Supabase Database Configuration (Option 2: Individual parameters)
# SUPABASE_DB_HOST=db.[YOUR-PROJECT-REF].supabase.co
# SUPABASE_DB_NAME=postgres
# SUPABASE_DB_USER=postgres
# SUPABASE_DB_PASS=[YOUR-PASSWORD]
# SUPABASE_DB_PORT=5432

# Local Database Configuration (fallback)
# If no Supabase config is provided, will use local PostgreSQL
# DB_HOST=localhost
# DB_NAME=sklad
# DB_USER=postgres
# DB_PASS=postgres

# Cache Configuration
CACHE_ENABLED=true
CACHE_DURATION=3600

# Performance Settings
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=100

# API Settings
API_RATE_LIMIT=1000
API_TIMEOUT=30

# Receipt Settings
RECEIPT_TEMPLATE_PATH=@app/templates/receipts
PDF_TEMP_PATH=@runtime/pdf

# Logging
LOG_LEVEL=error
LOG_FILE_SIZE=10MB
LOG_MAX_FILES=5
