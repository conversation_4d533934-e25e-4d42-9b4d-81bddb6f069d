a:14:{s:6:"config";s:1021:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:13:"Warehouse API";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:3:{s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.26.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:56:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-gii/src";}}}}";s:3:"log";s:9065:"a:1:{s:8:"messages";a:14:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.27774;i:4;a:0:{}i:5;i:2744120;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.277769;i:4;a:0:{}i:5;i:2745296;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.290404;i:4;a:0:{}i:5;i:3640704;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.293761;i:4;a:0:{}i:5;i:4300528;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.294065;i:4;a:0:{}i:5;i:4325360;}i:51;a:6:{i:0;s:33:"Route requested: 'api/auth/login'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.29622;i:4;a:0:{}i:5;i:4586296;}i:52;a:6:{i:0;s:28:"Route to run: api/auth/login";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.298307;i:4;a:0:{}i:5;i:4838744;}i:53;a:6:{i:0;s:65:"Running action: app\controllers\api\AuthController::actionLogin()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.299555;i:4;a:0:{}i:5;i:4942104;}i:54;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.31164;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6522280;}i:57;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.36735;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6661688;}i:60;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40327;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6710128;}i:63;a:6:{i:0;s:67:"SELECT * FROM "user" WHERE ("username"='testuser') AND ("status"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410408;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7073408;}i:66;a:6:{i:0;s:55:"User '4' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1748973528.11544;i:4;a:2:{i:0;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:63;s:8:"function";s:5:"login";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\warehouse\controllers\api\AuthController.php";s:4:"line";i:43;s:8:"function";s:5:"login";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7272768;}i:67;a:6:{i:0;s:63:"Slow API request: api/auth/login took 818.37ms (Memory: 0.00MB)";i:1;i:2;i:2;s:11:"performance";i:3;d:1748973528.116449;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php";s:4:"line";i:108;s:8:"function";s:7:"warning";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php";s:4:"line";i:68;s:8:"function";s:14:"logSlowRequest";s:5:"class";s:33:"app\components\PerformanceMonitor";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\warehouse\controllers\api\BaseController.php";s:4:"line";i:126;s:8:"function";s:10:"endRequest";s:5:"class";s:33:"app\components\PerformanceMonitor";s:4:"type";s:2:"->";}}i:5;i:7364448;}}}";s:9:"profiling";s:13032:"a:3:{s:6:"memory";i:7576792;s:4:"time";d:0.8551177978515625;s:8:"messages";a:8:{i:55;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.31166;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6524160;}i:56;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.365202;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6526904;}i:58;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367402;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6664016;}i:59;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40133;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6683552;}i:61;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.403321;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6712368;}i:62;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.406714;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6714680;}i:64;a:6:{i:0;s:67:"SELECT * FROM "user" WHERE ("username"='testuser') AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410448;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7076808;}i:65;a:6:{i:0;s:67:"SELECT * FROM "user" WHERE ("username"='testuser') AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415513;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7086136;}}}";s:2:"db";s:11514:"a:1:{s:8:"messages";a:6:{i:58;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.367402;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6664016;}i:59;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.40133;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6683552;}i:61;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.403321;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6712368;}i:62;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.406714;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:6714680;}i:64;a:6:{i:0;s:67:"SELECT * FROM "user" WHERE ("username"='testuser') AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.410448;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7076808;}i:65;a:6:{i:0;s:67:"SELECT * FROM "user" WHERE ("username"='testuser') AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.415513;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:113;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:76;s:8:"function";s:14:"findByUsername";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:49:"D:\OSPanel\domains\warehouse\models\LoginForm.php";s:4:"line";i:48;s:8:"function";s:7:"getUser";s:5:"class";s:20:"app\models\LoginForm";s:4:"type";s:2:"->";}}i:5;i:7086136;}}}";s:5:"event";s:2989:"a:17:{i:0;a:5:{s:4:"time";d:**********.295763;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.298406;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.299542;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"app\controllers\api\AuthController";}i:3;a:5:{s:4:"time";d:**********.303405;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"app\models\LoginForm";}i:4;a:5:{s:4:"time";d:**********.308881;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:5;a:5:{s:4:"time";d:**********.365186;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:6;a:5:{s:4:"time";d:**********.416628;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:7;a:5:{s:4:"time";d:**********.416686;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:8;a:5:{s:4:"time";d:1748973528.114101;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:20:"app\models\LoginForm";}i:9;a:5:{s:4:"time";d:1748973528.114386;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:10;a:5:{s:4:"time";d:1748973528.115465;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:11;a:5:{s:4:"time";d:1748973528.116494;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:34:"app\controllers\api\AuthController";}i:12;a:5:{s:4:"time";d:1748973528.116847;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:13;a:5:{s:4:"time";d:1748973528.116856;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:14;a:5:{s:4:"time";d:1748973528.116862;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:15;a:5:{s:4:"time";d:1748973528.117599;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:16;a:5:{s:4:"time";d:1748973528.117653;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.265597;s:3:"end";d:1748973528.120812;s:6:"memory";i:7576792;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:9030:"a:3:{s:8:"messages";a:46:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296044;i:4;a:0:{}i:5;i:4548672;}i:6;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296055;i:4;a:0:{}i:5;i:4549424;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.29606;i:4;a:0:{}i:5;i:4550176;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296064;i:4;a:0:{}i:5;i:4550928;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296068;i:4;a:0:{}i:5;i:4552000;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:16:"GET api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296077;i:4;a:0:{}i:5;i:4552800;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:17:"POST api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296083;i:4;a:0:{}i:5;i:4553600;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296086;i:4;a:0:{}i:5;i:4554408;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296089;i:4;a:0:{}i:5;i:4555216;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:27:"PATCH api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296092;i:4;a:0:{}i:5;i:4556024;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:28:"DELETE api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296095;i:4;a:0:{}i:5;i:4556832;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:18:"GET api/categories";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296098;i:4;a:0:{}i:5;i:4557632;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:19:"POST api/categories";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296103;i:4;a:0:{}i:5;i:4559072;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296106;i:4;a:0:{}i:5;i:4559880;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296108;i:4;a:0:{}i:5;i:4560688;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:29:"PATCH api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296111;i:4;a:0:{}i:5;i:4561496;}i:21;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296114;i:4;a:0:{}i:5;i:4562304;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:13:"GET api/sales";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296117;i:4;a:0:{}i:5;i:4563096;}i:23;a:6:{i:0;a:3:{s:4:"rule";s:14:"POST api/sales";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296121;i:4;a:0:{}i:5;i:4563888;}i:24;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET api/sales/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296124;i:4;a:0:{}i:5;i:4564688;}i:25;a:6:{i:0;a:3:{s:4:"rule";s:25:"DELETE api/sales/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296126;i:4;a:0:{}i:5;i:4565496;}i:26;a:6:{i:0;a:3:{s:4:"rule";s:15:"GET api/incomes";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296129;i:4;a:0:{}i:5;i:4566288;}i:27;a:6:{i:0;a:3:{s:4:"rule";s:16:"POST api/incomes";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296133;i:4;a:0:{}i:5;i:4567088;}i:28;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET api/incomes/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296136;i:4;a:0:{}i:5;i:4567896;}i:29;a:6:{i:0;a:3:{s:4:"rule";s:27:"DELETE api/incomes/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296139;i:4;a:0:{}i:5;i:4568704;}i:30;a:6:{i:0;a:3:{s:4:"rule";s:28:"POST api/income/scan-barcode";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296142;i:4;a:0:{}i:5;i:4569512;}i:31;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST api/income/quick-entry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296146;i:4;a:0:{}i:5;i:4570320;}i:32;a:6:{i:0;a:3:{s:4:"rule";s:21:"POST api/income/batch";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.29615;i:4;a:0:{}i:5;i:4571120;}i:33;a:6:{i:0;a:3:{s:4:"rule";s:21:"GET api/income/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296153;i:4;a:0:{}i:5;i:4573200;}i:34;a:6:{i:0;a:3:{s:4:"rule";s:27:"POST api/sales/scan-barcode";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296157;i:4;a:0:{}i:5;i:4574008;}i:35;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST api/sales/quick-entry";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296161;i:4;a:0:{}i:5;i:4574816;}i:36;a:6:{i:0;a:3:{s:4:"rule";s:20:"GET api/sales/search";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296164;i:4;a:0:{}i:5;i:4575616;}i:37;a:6:{i:0;a:3:{s:4:"rule";s:21:"GET api/sales/popular";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296166;i:4;a:0:{}i:5;i:4576416;}i:38;a:6:{i:0;a:3:{s:4:"rule";s:26:"POST api/receipts/generate";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.29617;i:4;a:0:{}i:5;i:4577224;}i:39;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET api/receipts/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296173;i:4;a:0:{}i:5;i:4578032;}i:40;a:6:{i:0;a:3:{s:4:"rule";s:32:"POST api/receipts/<id:\d+>/print";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296181;i:4;a:0:{}i:5;i:4578848;}i:41;a:6:{i:0;a:3:{s:4:"rule";s:36:"POST api/receipts/<id:\d+>/duplicate";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296184;i:4;a:0:{}i:5;i:4579664;}i:42;a:6:{i:0;a:3:{s:4:"rule";s:24:"GET api/receipts/history";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296187;i:4;a:0:{}i:5;i:4580472;}i:43;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET api/receipts/statistics";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.29619;i:4;a:0:{}i:5;i:4581280;}i:44;a:6:{i:0;a:3:{s:4:"rule";s:35:"GET api/receipts/by-number/<number>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296193;i:4;a:0:{}i:5;i:4582096;}i:45;a:6:{i:0;a:3:{s:4:"rule";s:21:"GET api/reports/stock";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296196;i:4;a:0:{}i:5;i:4582896;}i:46;a:6:{i:0;a:3:{s:4:"rule";s:21:"GET api/reports/sales";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296199;i:4;a:0:{}i:5;i:4583696;}i:47;a:6:{i:0;a:3:{s:4:"rule";s:22:"GET api/reports/income";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296202;i:4;a:0:{}i:5;i:4584496;}i:48;a:6:{i:0;a:3:{s:4:"rule";s:23:"GET api/reports/summary";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296204;i:4;a:0:{}i:5;i:4585296;}i:49;a:6:{i:0;s:44:"Request parsed with URL rule: api/auth/login";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.296211;i:4;a:0:{}i:5;i:4586576;}i:50;a:6:{i:0;a:3:{s:4:"rule";s:19:"POST api/auth/login";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.296214;i:4;a:0:{}i:5;i:4586928;}}s:5:"route";s:14:"api/auth/login";s:6:"action";s:49:"app\controllers\api\AuthController::actionLogin()";}";s:7:"request";s:2238:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:4:{s:4:"host";s:14:"localhost:8080";s:12:"content-type";s:16:"application/json";s:6:"accept";s:16:"application/json";s:14:"content-length";s:2:"48";}s:15:"responseHeaders";a:7:{s:12:"X-Powered-By";s:10:"PHP/8.1.31";s:32:"Access-Control-Allow-Credentials";s:4:"true";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683f37d747099";s:16:"X-Debug-Duration";s:3:"853";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683f37d747099";s:10:"Set-Cookie";s:204:"_csrf=31e7942d5dcbc1bd637ad660b7cf904abf064754999bee43cb4f5c274c2db958a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22qy566j24VffxUVTFnVEabWNbk4IFSGAd%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:14:"api/auth/login";s:6:"action";s:49:"app\controllers\api\AuthController::actionLogin()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:3:{s:12:"Content Type";s:16:"application/json";s:3:"Raw";s:48:"{"username":"testuser","password":"testpass123"}";s:7:"Decoded";a:2:{s:8:"username";s:8:"testuser";s:8:"password";s:11:"testpass123";}}s:6:"SERVER";a:21:{s:13:"DOCUMENT_ROOT";s:32:"D:\OSPanel\domains\warehouse\web";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:11:"REMOTE_PORT";s:5:"61568";s:15:"SERVER_SOFTWARE";s:29:"PHP 8.1.31 Development Server";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_PORT";s:4:"8080";s:11:"REQUEST_URI";s:15:"/api/auth/login";s:14:"REQUEST_METHOD";s:4:"POST";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"D:\OSPanel\domains\warehouse\web\index.php";s:9:"PATH_INFO";s:15:"/api/auth/login";s:8:"PHP_SELF";s:25:"/index.php/api/auth/login";s:9:"HTTP_HOST";s:14:"localhost:8080";s:12:"CONTENT_TYPE";s:16:"application/json";s:17:"HTTP_CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ACCEPT";s:16:"application/json";s:14:"CONTENT_LENGTH";s:2:"48";s:19:"HTTP_CONTENT_LENGTH";s:2:"48";s:18:"REQUEST_TIME_FLOAT";d:**********.264374;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:1212:"a:5:{s:2:"id";i:4;s:8:"identity";a:10:{s:2:"id";s:1:"4";s:8:"username";s:10:"'testuser'";s:5:"email";s:18:"'<EMAIL>'";s:13:"password_hash";s:62:"'$2y$13$/Rwhn432uv8dcP2UptJ6CuZbhhb.u./aGDIzCJJQ6P7TgEC3j4squ'";s:8:"auth_key";s:34:"'P0da2g3e0XpmSCIIwFn3MeR61zJnTCOr'";s:4:"role";s:7:"'admin'";s:6:"status";s:1:"1";s:10:"created_at";s:21:"'2025-06-03 22:57:55'";s:10:"updated_at";s:21:"'2025-06-03 22:57:55'";s:13:"last_login_at";s:4:"null";}s:10:"attributes";a:10:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:5;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:13:"last_login_at";s:5:"label";s:13:"Last Login At";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683f37d747099";s:3:"url";s:36:"http://localhost:8080/api/auth/login";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.264374;s:10:"statusCode";i:200;s:8:"sqlCount";i:3;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7576792;s:14:"processingTime";d:0.8551177978515625;}s:10:"exceptions";a:0:{}}