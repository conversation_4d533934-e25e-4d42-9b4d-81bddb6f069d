<?php

namespace app\models;

use yii\db\ActiveRecord;
use yii\web\Link;
use yii\web\Linkable;
use yii\helpers\Url;

/**
 * Base model class with enhanced API support
 */
abstract class ApiModel extends ActiveRecord implements Linkable
{
    /**
     * @inheritdoc
     * Return fields that should be included in API response
     */
    public function fields()
    {
        $fields = parent::fields();
        
        // Remove sensitive fields that should not be exposed via API
        if (isset($fields['password'])) {
            unset($fields['password']);
        }
        if (isset($fields['auth_key'])) {
            unset($fields['auth_key']);
        }
        if (isset($fields['password_hash'])) {
            unset($fields['password_hash']);
        }
        
        return $fields;
    }
    
    /**
     * @inheritdoc
     * Return extra fields that can be included in API response
     */
    public function extraFields()
    {
        return [];
    }
    
    /**
     * @inheritdoc
     * Returns links to related resources
     */
    public function getLinks()
    {
        $links = [];
        $modelName = strtolower((new \ReflectionClass($this))->getShortName());
        $links['self'] = Url::to(['api/' . $modelName . '/view', 'id' => $this->getPrimaryKey()], true);
        
        return $links;
    }
}
