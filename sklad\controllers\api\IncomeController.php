<?php

namespace app\controllers\api;

use Yii;
use app\models\Income;
use app\models\Product;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;

/**
 * IncomeController implements the API endpoints for Income model
 */
class IncomeController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'index' => ['GET'],
            'view' => ['GET'],
            'create' => ['POST'],
            'delete' => ['DELETE'],
        ];
    }    /**
     * Lists all income records with filtering, sorting and pagination
     * @return array
     */
    public function actionIndex()
    {
        $query = Income::find()->with('product');
        $request = Yii::$app->request;
        
        // Apply date range filters
        if ($startDate = $request->get('start_date')) {
            $query->andWhere(['>=', 'DATE(created_at)', $startDate]);
        }
        
        if ($endDate = $request->get('end_date')) {
            $query->andWhere(['<=', 'DATE(created_at)', $endDate]);
        }
        
        // Apply product filter
        if ($productId = $request->get('product_id')) {
            $query->andWhere(['product_id' => $productId]);
        }
        
        // Apply cost range filters
        if ($minCost = $request->get('min_cost')) {
            $query->andWhere(['>=', 'total_cost', $minCost]);
        }
        
        if ($maxCost = $request->get('max_cost')) {
            $query->andWhere(['<=', 'total_cost', $maxCost]);
        }
        
        // Create data provider with default sorting
        $dataProvider = $this->createDataProvider($query, ['created_at' => SORT_DESC]);
        
        // Return paginated response
        return $this->paginatedResponse($dataProvider);
    }

    /**
     * Displays a specific income record
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->successResponse($model);
    }

    /**
     * Creates a new income record
     * @return array
     */
    public function actionCreate()
    {
        $model = new Income();
        
        if ($model->load(Yii::$app->request->post(), '')) {
            // Check product existence
            $product = Product::findOne($model->product_id);
            if (!$product) {
                return $this->errorResponse('Товар не найден', 404);
            }
            
            if ($model->save()) {
                // Return the income with related product data
                $result = $model->toArray();
                $result['product'] = $model->product->toArray();
                
                return $this->successResponse($result, 201);
            }
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Deletes an income record
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        // Note: In a real-world app, you might want to prevent deletion of income records 
        // or implement a reversal of stock changes when an income record is deleted
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            return $this->successResponse(null, 204);
        }
        
        return $this->errorResponse('Не удалось удалить приход', 500);
    }

    /**
     * Finds the Income model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id
     * @return Income the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Income::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('Приход не найден');
    }
}
