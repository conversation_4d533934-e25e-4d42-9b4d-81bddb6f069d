# Warehouse API

This is an API-only version of the Warehouse Management System built with Yii2 framework.

## Features

- RESTful API for warehouse management
- JWT authentication
- CORS support for cross-domain requests
- Comprehensive API documentation

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher
- Composer

## Installation

1. Clone the repository:
```
git clone https://github.com/your-username/warehouse-api.git
cd warehouse-api
```

2. Install dependencies:
```
composer install
```

3. Create the database and configure the connection in `config/db.php`.

4. Run migrations to create database tables:
```
./yii migrate
```

5. Change the `jwtSecret` in `config/params.php` to a secure random string:
```php
'jwtSecret' => 'your-secure-secret-key',
```

## Running the API

You can run the application using PHP's built-in server:

```
php yii serve
```

Or configure a web server like Apache or Nginx to serve the application.

## API Documentation

See the [API_DOCUMENTATION.md](API_DOCUMENTATION.md) file for complete API documentation.

### Basic Usage

#### Authentication

```
POST /api/auth/login

{
  "username": "admin",
  "password": "admin"
}
```

Include the JWT token in the Authorization header for subsequent requests:
```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### Example Endpoints

- GET /api/products - List all products
- POST /api/products - Create a new product
- GET /api/products/{id} - Get product details
- PUT /api/products/{id} - Update a product
- DELETE /api/products/{id} - Delete a product

See the API documentation for more endpoints and details.

## Security

This API implementation includes:

- JWT-based authentication
- CORS configuration for cross-domain requests
- Input validation
- Error handling

In a production environment, consider implementing:

- Rate limiting
- HTTPS enforcement
- Additional security headers

## Credits

Based on the [Yii2 Framework](https://www.yiiframework.com/).
