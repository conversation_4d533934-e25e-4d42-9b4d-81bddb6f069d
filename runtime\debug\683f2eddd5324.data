a:10:{s:6:"config";s:1022:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:3:{s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.26.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:56:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-gii/src";}}}}";s:3:"log";s:1811:"a:1:{s:8:"messages";a:6:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748971229.863727;i:4;a:0:{}i:5;i:2400384;}i:1;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748971229.86378;i:4;a:0:{}i:5;i:2401184;}i:2;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748971229.865761;i:4;a:0:{}i:5;i:2430248;}i:3;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:1748971229.865781;i:4;a:0:{}i:5;i:2431424;}i:4;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:1748971229.872711;i:4;a:0:{}i:5;i:2934744;}i:5;a:6:{i:0;s:919:"yii\console\Exception: Unknown option: --limit. Options available: --color, --interactive, --help, --silentExitOnException, --migrationPath, --migrationNamespaces, --compact, --migrationTable, --db in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction('history', Array)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction('migrate/history', Array)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/history', Array)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#4 D:\OSPanel\domains\warehouse\yii(20): yii\base\Application->run()
#5 {main}";i:1;i:1;i:2;s:21:"yii\console\Exception";i:3;d:1748971229.888145;i:4;a:0:{}i:5;i:3663136;}}}";s:9:"profiling";s:83:"a:3:{s:6:"memory";i:4328904;s:4:"time";d:0.21931791305541992;s:8:"messages";a:0:{}}";s:2:"db";s:27:"a:1:{s:8:"messages";a:0:{}}";s:5:"event";s:186:"a:1:{i:0;a:5:{s:4:"time";d:1748971229.876189;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:23:"yii\console\Application";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:1748971229.854469;s:3:"end";d:1748971230.073802;s:6:"memory";i:4328904;}";s:4:"dump";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683f2eddd5324";s:3:"url";s:34:"php yii migrate/history --limit=20";s:4:"ajax";i:0;s:6:"method";s:7:"COMMAND";s:2:"ip";s:20:"win-qpo53k8ffa4\user";s:4:"time";d:1748971229.83648;s:10:"statusCode";i:0;s:8:"sqlCount";i:0;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:4328904;s:14:"processingTime";d:0.21931791305541992;}s:10:"exceptions";a:0:{}}