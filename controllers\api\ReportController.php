<?php

namespace app\controllers\api;

use Yii;
use app\models\Product;
use app\models\Sale;
use app\models\Income;
use yii\helpers\ArrayHelper;
use yii\db\Expression;

/**
 * ReportController implements API endpoints for various warehouse reports
 */
class ReportController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'stock' => ['GET'],
            'sales' => ['GET'],
            'income' => ['GET'],
            'summary' => ['GET'],
        ];
    }

    /**
     * Get stock report - current stock levels of all products
     * GET /api/reports/stock
     *
     * @return array
     */
    public function actionStock()
    {
        $request = Yii::$app->request;
        $categoryId = $request->get('category_id');
        $lowStock = $request->get('low_stock');
        $limit = min((int)$request->get('limit', 100), 1000);

        try {
            $query = Product::find()->with('category');

            // Apply filters
            if ($categoryId) {
                $query->andWhere(['category_id' => $categoryId]);
            }

            if ($lowStock !== null) {
                $query->andWhere(['<=', 'current_stock', $lowStock]);
            }

            $query->orderBy(['name' => SORT_ASC])->limit($limit);
            $products = $query->all();

            // Calculate summary
            $totalProducts = count($products);
            $totalStockValue = 0;
            $lowStockCount = 0;

            $productData = [];
            foreach ($products as $product) {
                $stockValue = $product->current_stock * $product->price_per_unit;
                $totalStockValue += $stockValue;

                if ($product->current_stock <= 5) {
                    $lowStockCount++;
                }

                $productData[] = [
                    'id' => $product->id,
                    'name' => $product->name,
                    'barcode' => $product->barcode,
                    'current_stock' => $product->current_stock,
                    'unit_type' => $product->unit_type,
                    'price_per_unit' => $product->price_per_unit,
                    'stock_value' => $stockValue,
                    'category_name' => $product->category ? $product->category->name : null,
                ];
            }

            return $this->successResponse([
                'report_type' => 'stock',
                'generated_at' => date('Y-m-d H:i:s'),
                'filters' => [
                    'category_id' => $categoryId,
                    'low_stock' => $lowStock,
                    'limit' => $limit,
                ],
                'products' => $productData,
                'summary' => [
                    'total_products' => $totalProducts,
                    'total_stock_value' => round($totalStockValue, 2),
                    'low_stock_count' => $lowStockCount,
                ],
                'operation_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Stock report failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get sales report by date range
     * GET /api/reports/sales
     *
     * @return array
     */
    public function actionSales()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-01'));
        $endDate = $request->get('end_date', date('Y-m-t'));
        $productId = $request->get('product_id');
        $groupBy = $request->get('group_by');
        $limit = min((int)$request->get('limit', 100), 1000);

        // Validate date range
        if ($startDate > $endDate) {
            return $this->errorResponse('Start date must be before end date', 400);
        }

        try {
            $query = Sale::find()->with('product');

            // Apply date filters
            $query->andWhere(['>=', 'created_at', $startDate . ' 00:00:00']);
            $query->andWhere(['<=', 'created_at', $endDate . ' 23:59:59']);

            // Apply product filter
            if ($productId) {
                $query->andWhere(['product_id' => $productId]);
            }

            $query->orderBy(['created_at' => SORT_DESC])->limit($limit);
            $sales = $query->all();

            // Calculate summary
            $totalSales = count($sales);
            $totalAmount = 0;
            $totalQuantity = 0;

            $salesData = [];
            foreach ($sales as $sale) {
                $totalAmount += $sale->quantity * $sale->price_per_unit;
                $totalQuantity += $sale->quantity;

                $salesData[] = [
                    'id' => $sale->id,
                    'product_id' => $sale->product_id,
                    'product_name' => $sale->product ? $sale->product->name : null,
                    'product_barcode' => $sale->product ? $sale->product->barcode : null,
                    'quantity' => $sale->quantity,
                    'price_per_unit' => $sale->price_per_unit,
                    'total_amount' => $sale->quantity * $sale->price_per_unit,
                    'notes' => $sale->notes,
                    'created_at' => $sale->created_at,
                ];
            }

            return $this->successResponse([
                'report_type' => 'sales',
                'generated_at' => date('Y-m-d H:i:s'),
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ],
                'filters' => [
                    'product_id' => $productId,
                    'group_by' => $groupBy,
                    'limit' => $limit,
                ],
                'sales' => $salesData,
                'summary' => [
                    'total_sales' => $totalSales,
                    'total_amount' => round($totalAmount, 2),
                    'total_quantity' => $totalQuantity,
                    'average_sale_amount' => $totalSales > 0 ? round($totalAmount / $totalSales, 2) : 0,
                ],
                'operation_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Sales report failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get income report by date range
     * GET /api/reports/income
     *
     * @return array
     */
    public function actionIncome()
    {
        $request = Yii::$app->request;
        $startDate = $request->get('start_date', date('Y-m-01'));
        $endDate = $request->get('end_date', date('Y-m-t'));
        $productId = $request->get('product_id');
        $limit = min((int)$request->get('limit', 100), 1000);

        // Validate date range
        if ($startDate > $endDate) {
            return $this->errorResponse('Start date must be before end date', 400);
        }

        try {
            $query = Income::find()->with('product');

            // Apply date filters
            $query->andWhere(['>=', 'created_at', $startDate . ' 00:00:00']);
            $query->andWhere(['<=', 'created_at', $endDate . ' 23:59:59']);

            // Apply product filter
            if ($productId) {
                $query->andWhere(['product_id' => $productId]);
            }

            $query->orderBy(['created_at' => SORT_DESC])->limit($limit);
            $incomes = $query->all();

            // Calculate summary
            $totalIncomes = count($incomes);
            $totalAmount = 0;
            $totalQuantity = 0;

            $incomeData = [];
            foreach ($incomes as $income) {
                $totalAmount += $income->quantity * $income->price_per_unit;
                $totalQuantity += $income->quantity;

                $incomeData[] = [
                    'id' => $income->id,
                    'product_id' => $income->product_id,
                    'product_name' => $income->product ? $income->product->name : null,
                    'product_barcode' => $income->product ? $income->product->barcode : null,
                    'quantity' => $income->quantity,
                    'price_per_unit' => $income->price_per_unit,
                    'total_amount' => $income->quantity * $income->price_per_unit,
                    'notes' => $income->notes,
                    'created_at' => $income->created_at,
                ];
            }

            return $this->successResponse([
                'report_type' => 'income',
                'generated_at' => date('Y-m-d H:i:s'),
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate,
                ],
                'filters' => [
                    'product_id' => $productId,
                    'limit' => $limit,
                ],
                'incomes' => $incomeData,
                'summary' => [
                    'total_incomes' => $totalIncomes,
                    'total_amount' => round($totalAmount, 2),
                    'total_quantity' => $totalQuantity,
                    'average_income_amount' => $totalIncomes > 0 ? round($totalAmount / $totalIncomes, 2) : 0,
                ],
                'operation_time' => microtime(true) - $_SERVER['REQUEST_TIME_FLOAT']
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Income report failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Get summary report with statistics
     * @param string $start Start date (Y-m-d)
     * @param string $end End date (Y-m-d)
     * @return array
     */
    public function actionSummary($start = null, $end = null)
    {
        // Default to current month if no dates provided
        if ($start === null && $end === null) {
            $start = date('Y-m-01'); // First day of current month
            $end = date('Y-m-t');    // Last day of current month
        }

        // Total sales
        $salesQuery = Sale::find();
        if ($start !== null) {
            $salesQuery->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        if ($end !== null) {
            $salesQuery->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        // Calculate total sales manually since we don't have total_amount field
        $sales = $salesQuery->all();
        $totalSales = 0;
        foreach ($sales as $sale) {
            $totalSales += $sale->quantity * $sale->price_per_unit;
        }
        $salesCount = $salesQuery->count();

        // Total income/purchases
        $incomeQuery = Income::find();
        if ($start !== null) {
            $incomeQuery->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        if ($end !== null) {
            $incomeQuery->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        // Calculate total income manually since we don't have total_cost field
        $incomes = $incomeQuery->all();
        $totalIncome = 0;
        foreach ($incomes as $income) {
            $totalIncome += $income->quantity * $income->price_per_unit;
        }
        $incomeCount = $incomeQuery->count();

        // Profit calculation
        $profit = $totalSales - $totalIncome;

        // Current inventory value
        $products = Product::find()->all();
        $inventoryValue = 0;
        foreach ($products as $product) {
            $inventoryValue += $product->current_stock * $product->price_per_unit;
        }

        // Product count
        $productsCount = Product::find()->count();

        $data = [
            'period' => [
                'start' => $start,
                'end' => $end,
            ],
            'sales' => [
                'total' => (float)$totalSales,
                'count' => (int)$salesCount,
            ],
            'purchases' => [
                'total' => (float)$totalIncome,
                'count' => (int)$incomeCount,
            ],
            'profit' => (float)$profit,
            'inventory' => [
                'value' => (float)$inventoryValue,
                'products_count' => (int)$productsCount,
            ],
        ];

        return $this->successResponse($data);
    }
}
