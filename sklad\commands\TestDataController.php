<?php

namespace app\commands;

use Yii;
use app\models\Category;
use app\models\Product;
use app\models\Income;
use yii\console\Controller;

/**
 * Test data seeder
 */
class TestDataController extends Controller
{
    /**
     * Seed test data
     */
    public function actionSeed()
    {
        $this->stdout("Creating test data...\n");
        
        // Create categories
        $categories = [
            ['name' => 'Напитки', 'description' => 'Безалкогольные и алкогольные напитки'],
            ['name' => 'Молочные продукты', 'description' => 'Молоко, сыры, йогурты'],
            ['name' => 'Хлебобулочные изделия', 'description' => 'Хлеб, булочки, выпечка'],
            ['name' => 'Мясные продукты', 'description' => 'Мясо, колбасы, сосиски'],
            ['name' => 'Овощи и фрукты', 'description' => 'Свежие овощи и фрукты'],
        ];

        foreach ($categories as $categoryData) {
            $category = new Category();
            $category->name = $categoryData['name'];
            $category->description = $categoryData['description'];
            if ($category->save()) {
                $this->stdout("✓ Created category: {$category->name}\n");
            }
        }

        // Get created categories
        $drinkCategory = Category::findOne(['name' => 'Напитки']);
        $milkCategory = Category::findOne(['name' => 'Молочные продукты']);
        $breadCategory = Category::findOne(['name' => 'Хлебобулочные изделия']);
        $meatCategory = Category::findOne(['name' => 'Мясные продукты']);
        $vegetableCategory = Category::findOne(['name' => 'Овощи и фрукты']);

        // Create products
        $products = [
            // Напитки
            [
                'name' => 'Coca-Cola 0.5л',
                'barcode' => '4607177411243',
                'category_id' => $drinkCategory->id,
                'unit_type' => Product::UNIT_PIECE,
                'price_per_unit' => 89.90,
                'current_stock' => 0,
                'description' => 'Газированный напиток Кока-Кола 0.5л',
            ],
            [
                'name' => 'Вода минеральная Ессентуки №4 0.5л',
                'barcode' => '4607034171759',
                'category_id' => $drinkCategory->id,
                'unit_type' => Product::UNIT_PIECE,
                'price_per_unit' => 45.50,
                'current_stock' => 0,
                'description' => 'Лечебно-столовая вода',
            ],
            
            // Молочные продукты
            [
                'name' => 'Молоко Домик в деревне 3.2% 930мл',
                'barcode' => '4607096261567',
                'category_id' => $milkCategory->id,
                'unit_type' => Product::UNIT_PIECE,
                'price_per_unit' => 129.00,
                'current_stock' => 0,
                'description' => 'Ультрапастеризованное молоко',
            ],
            [
                'name' => 'Сыр Российский',
                'barcode' => '4607123456789',
                'category_id' => $milkCategory->id,
                'unit_type' => Product::UNIT_KG,
                'price_per_unit' => 850.00,
                'current_stock' => 0,
                'description' => 'Твердый сыр Российский',
            ],
            
            // Хлебобулочные изделия
            [
                'name' => 'Хлеб Бородинский нарезной',
                'barcode' => '4607987654321',
                'category_id' => $breadCategory->id,
                'unit_type' => Product::UNIT_PIECE,
                'price_per_unit' => 65.00,
                'current_stock' => 0,
                'description' => 'Хлеб ржано-пшеничный',
            ],
            
            // Мясные продукты
            [
                'name' => 'Колбаса Докторская',
                'barcode' => '4607111222333',
                'category_id' => $meatCategory->id,
                'unit_type' => Product::UNIT_KG,
                'price_per_unit' => 680.00,
                'current_stock' => 0,
                'description' => 'Вареная колбаса высшего сорта',
            ],
            
            // Овощи и фрукты
            [
                'name' => 'Яблоки Гала',
                'barcode' => '4607555666777',
                'category_id' => $vegetableCategory->id,
                'unit_type' => Product::UNIT_KG,
                'price_per_unit' => 189.00,
                'current_stock' => 0,
                'description' => 'Свежие яблоки сорта Гала',
            ],
            [
                'name' => 'Бананы',
                'barcode' => '4607888999000',
                'category_id' => $vegetableCategory->id,
                'unit_type' => Product::UNIT_KG,
                'price_per_unit' => 159.00,
                'current_stock' => 0,
                'description' => 'Спелые бананы',
            ],
        ];

        foreach ($products as $productData) {
            $product = new Product();
            $product->attributes = $productData;
            if ($product->save()) {
                $this->stdout("✓ Created product: {$product->name}\n");
                
                // Add initial stock
                $income = new Income();
                $income->product_id = $product->id;
                $income->quantity = rand(10, 50);
                $income->price_per_unit = $product->price_per_unit * 0.7; // Cost price
                $income->total_cost = $income->quantity * $income->price_per_unit;
                $income->notes = 'Первоначальная поставка';
                
                if ($income->save()) {
                    $this->stdout("  ✓ Added initial stock: {$income->quantity} units\n");
                }
            }
        }

        $this->stdout("\n✅ Test data created successfully!\n");
        $this->stdout("📊 You can now test the warehouse management system.\n");
    }

    /**
     * Clear all test data
     */
    public function actionClear()
    {
        $this->stdout("Clearing test data...\n");
        
        Income::deleteAll();
        Product::deleteAll();
        Category::deleteAll();
        
        $this->stdout("✅ Test data cleared!\n");
    }
}
