<?php
/**
 * This script identifies and optionally removes frontend files 
 * when transitioning to an API-only application.
 * 
 * Usage: php remove_frontend_files.php [--execute]
 * Without the --execute flag, it will only list files that would be removed
 */

// Configuration
$basePath = __DIR__;
$dryRun = true; // Just list files by default

// Check for execute flag
if (isset($argv[1]) && $argv[1] === '--execute') {
    $dryRun = false;
    echo "WARNING: Files will be removed. Are you sure? (y/n): ";
    $handle = fopen("php://stdin", "r");
    $line = fgets($handle);
    if(trim(strtolower($line)) != 'y') {
        echo "Operation cancelled.\n";
        exit;
    }
    fclose($handle);
}

// Directories that contain only frontend files
$frontendDirectories = [
    'web/css',
    'web/js',
    'assets',
    'views',
    'widgets',
];

// File extensions that are typically frontend-related
$frontendExtensions = [
    '.css', '.scss', '.less',
    '.js', '.vue', '.jsx', 
    '.html', '.twig', '.phtml', 
    '.png', '.jpg', '.jpeg', '.gif', '.svg',
    '.woff', '.woff2', '.ttf', '.eot'
];

// Specific files to keep even if they match frontend patterns
$filesToKeep = [
    'web/index.php',
    'web/index-test.php',
    'web/robots.txt',
    'assets/AppAsset.php' // This might be modified for API needs
];

// Files to remove by exact path
$specificFilesToRemove = [
    'web/manifest.json',
    'web/offline.html',
    'web/sw.js',
];

$removedCount = 0;
$keptCount = 0;
$totalSize = 0;

echo "Scanning for frontend files...\n";

// Process specific files to remove
foreach ($specificFilesToRemove as $file) {
    $fullPath = $basePath . '/' . $file;
    if (file_exists($fullPath)) {
        if ($dryRun) {
            echo "[WOULD REMOVE] $file\n";
            $fileSize = filesize($fullPath);
            $totalSize += $fileSize;
            $removedCount++;
        } else {
            if (unlink($fullPath)) {
                echo "Removed: $file\n";
                $removedCount++;
            } else {
                echo "Failed to remove: $file\n";
            }
        }
    }
}

// Process frontend directories
foreach ($frontendDirectories as $dir) {
    $fullPath = $basePath . '/' . $dir;
    if (!is_dir($fullPath)) {
        continue;
    }
    
    // Use RecursiveDirectoryIterator to iterate through all files
    $objects = new RecursiveIteratorIterator(
        new RecursiveDirectoryIterator($fullPath, RecursiveDirectoryIterator::SKIP_DOTS),
        RecursiveIteratorIterator::SELF_FIRST
    );
    
    foreach ($objects as $name => $object) {
        if (!$object->isDir()) {
            $relativePath = str_replace($basePath . '/', '', $name);
            
            // Check if this file should be kept
            if (in_array($relativePath, $filesToKeep)) {
                echo "[KEEPING] $relativePath\n";
                $keptCount++;
                continue;
            }
            
            // For view files (.php in views directory), these are frontend templates
            if (strpos($relativePath, 'views/') === 0 && pathinfo($relativePath, PATHINFO_EXTENSION) === 'php') {
                if ($dryRun) {
                    echo "[WOULD REMOVE] $relativePath\n";
                    $fileSize = filesize($name);
                    $totalSize += $fileSize;
                    $removedCount++;
                } else {
                    if (unlink($name)) {
                        echo "Removed: $relativePath\n";
                        $removedCount++;
                    } else {
                        echo "Failed to remove: $relativePath\n";
                    }
                }
                continue;
            }
            
            // Check if file has a frontend extension
            $extension = strtolower(pathinfo($relativePath, PATHINFO_EXTENSION));
            if (in_array('.' . $extension, $frontendExtensions)) {
                if ($dryRun) {
                    echo "[WOULD REMOVE] $relativePath\n";
                    $fileSize = filesize($name);
                    $totalSize += $fileSize;
                    $removedCount++;
                } else {
                    if (unlink($name)) {
                        echo "Removed: $relativePath\n";
                        $removedCount++;
                    } else {
                        echo "Failed to remove: $relativePath\n";
                    }
                }
            }
        }
    }
}

// Remove empty directories after files are removed (if not in dry run mode)
if (!$dryRun) {
    foreach ($frontendDirectories as $dir) {
        removeEmptySubdirs($basePath . '/' . $dir);
    }
}

echo "\nSummary:\n";
if ($dryRun) {
    echo "Found $removedCount frontend files that would be removed (" . formatBytes($totalSize) . ")\n";
    echo "$keptCount files would be kept\n";
    echo "This was a dry run. Use --execute parameter to actually remove files.\n";
} else {
    echo "Removed $removedCount frontend files\n";
    echo "$keptCount files were kept\n";
    echo "Operation completed.\n";
}

/**
 * Recursively removes empty directories
 */
function removeEmptySubdirs($path) {
    if (!is_dir($path)) {
        return;
    }
    
    $empty = true;
    $objects = scandir($path);
    
    foreach ($objects as $object) {
        if ($object != "." && $object != "..") {
            $fullPath = $path . "/" . $object;
            
            if (is_dir($fullPath)) {
                if (!removeEmptySubdirs($fullPath)) {
                    $empty = false;
                }
            } else {
                $empty = false;
            }
        }
    }
    
    if ($empty) {
        rmdir($path);
        echo "Removed empty directory: " . $path . "\n";
        return true;
    }
    
    return false;
}

/**
 * Format bytes to human-readable form
 */
function formatBytes($bytes, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, $precision) . ' ' . $units[$pow];
}
