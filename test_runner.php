<?php

// Simple test runner for API endpoints
require_once 'vendor/autoload.php';

// Initialize Yii application
defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'test');

require 'vendor/yiisoft/yii2/Yii.php';

$config = require 'config/web.php';
$app = new yii\web\Application($config);

class ApiTester
{
    private $baseUrl = 'http://localhost:8080';
    private $authToken = null;
    
    public function runAllTests()
    {
        echo "=== API Test Runner ===\n\n";
        
        // Test 1: Authentication
        echo "1. Testing Authentication...\n";
        $this->testAuthentication();
        
        // Test 2: Products
        echo "\n2. Testing Products...\n";
        $this->testProducts();
        
        // Test 3: Categories
        echo "\n3. Testing Categories...\n";
        $this->testCategories();
        
        // Test 4: Quick Operations
        echo "\n4. Testing Quick Operations...\n";
        $this->testQuickOperations();
        
        echo "\n=== Tests Completed ===\n";
    }
    
    private function testAuthentication()
    {
        // Create test user in database first
        $this->createTestUser();
        
        // Test login
        $loginData = [
            'username' => 'testuser',
            'password' => 'testpass123'
        ];
        
        $response = $this->makeApiCall('POST', '/api/auth/login', $loginData);
        
        if ($response && isset($response['success']) && $response['success']) {
            $this->authToken = $response['data']['token'];
            echo "✅ Login successful\n";
            
            // Test token verification
            $verifyResponse = $this->makeApiCall('POST', '/api/auth/verify', [
                'token' => $this->authToken
            ]);
            
            if ($verifyResponse && $verifyResponse['data']['valid']) {
                echo "✅ Token verification successful\n";
            } else {
                echo "❌ Token verification failed\n";
            }
        } else {
            echo "❌ Login failed\n";
        }
    }
    
    private function testProducts()
    {
        if (!$this->authToken) {
            echo "❌ No auth token, skipping product tests\n";
            return;
        }
        
        // Get products
        $response = $this->makeAuthenticatedCall('GET', '/api/products');
        echo ($response ? "✅" : "❌") . " GET /api/products\n";
        
        // Create product
        $productData = [
            'name' => 'Test API Product',
            'barcode' => '9999999999999',
            'unit_type' => 'piece',
            'price_per_unit' => 25.99,
            'current_stock' => 10
        ];
        
        $createResponse = $this->makeAuthenticatedCall('POST', '/api/products', $productData);
        echo ($createResponse ? "✅" : "❌") . " POST /api/products\n";
        
        if ($createResponse && isset($createResponse['data']['id'])) {
            $productId = $createResponse['data']['id'];
            
            // Get single product
            $getResponse = $this->makeAuthenticatedCall('GET', "/api/products/{$productId}");
            echo ($getResponse ? "✅" : "❌") . " GET /api/products/{$productId}\n";
            
            // Update product
            $updateResponse = $this->makeAuthenticatedCall('PUT', "/api/products/{$productId}", [
                'name' => 'Updated Test Product'
            ]);
            echo ($updateResponse ? "✅" : "❌") . " PUT /api/products/{$productId}\n";
            
            // Delete product
            $deleteResponse = $this->makeAuthenticatedCall('DELETE', "/api/products/{$productId}");
            echo ($deleteResponse ? "✅" : "❌") . " DELETE /api/products/{$productId}\n";
        }
    }
    
    private function testCategories()
    {
        if (!$this->authToken) {
            echo "❌ No auth token, skipping category tests\n";
            return;
        }
        
        // Get categories
        $response = $this->makeAuthenticatedCall('GET', '/api/categories');
        echo ($response ? "✅" : "❌") . " GET /api/categories\n";
        
        // Create category
        $categoryData = [
            'name' => 'Test API Category',
            'description' => 'Category created by API test'
        ];
        
        $createResponse = $this->makeAuthenticatedCall('POST', '/api/categories', $categoryData);
        echo ($createResponse ? "✅" : "❌") . " POST /api/categories\n";
        
        if ($createResponse && isset($createResponse['data']['id'])) {
            $categoryId = $createResponse['data']['id'];
            
            // Delete category
            $deleteResponse = $this->makeAuthenticatedCall('DELETE', "/api/categories/{$categoryId}");
            echo ($deleteResponse ? "✅" : "❌") . " DELETE /api/categories/{$categoryId}\n";
        }
    }
    
    private function testQuickOperations()
    {
        if (!$this->authToken) {
            echo "❌ No auth token, skipping quick operations tests\n";
            return;
        }
        
        // Test search
        $searchResponse = $this->makeAuthenticatedCall('GET', '/api/income/search?q=test&limit=5');
        echo ($searchResponse ? "✅" : "❌") . " GET /api/income/search\n";
        
        $saleSearchResponse = $this->makeAuthenticatedCall('GET', '/api/sales/search?q=test&limit=5');
        echo ($saleSearchResponse ? "✅" : "❌") . " GET /api/sales/search\n";
        
        // Test popular products
        $popularResponse = $this->makeAuthenticatedCall('GET', '/api/sales/popular?limit=5');
        echo ($popularResponse ? "✅" : "❌") . " GET /api/sales/popular\n";
    }
    
    private function createTestUser()
    {
        try {
            $user = new \app\models\User();
            $user->username = 'testuser';
            $user->email = '<EMAIL>';
            $user->setPassword('testpass123');
            $user->role = \app\models\User::ROLE_ADMIN;
            $user->status = \app\models\User::STATUS_ACTIVE;
            
            // Delete existing test user if exists
            \app\models\User::deleteAll(['username' => 'testuser']);
            
            if ($user->save()) {
                echo "✅ Test user created\n";
            } else {
                echo "❌ Failed to create test user: " . json_encode($user->getErrors()) . "\n";
            }
        } catch (Exception $e) {
            echo "❌ Error creating test user: " . $e->getMessage() . "\n";
        }
    }
    
    private function makeAuthenticatedCall($method, $endpoint, $data = null)
    {
        $headers = [];
        if ($this->authToken) {
            $headers[] = "Authorization: Bearer {$this->authToken}";
        }
        
        return $this->makeApiCall($method, $endpoint, $data, $headers);
    }
    
    private function makeApiCall($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "CURL Error: {$error}\n";
            return false;
        }
        
        if ($httpCode >= 400) {
            echo "HTTP Error {$httpCode} for {$method} {$endpoint}\n";
            if ($response) {
                echo "Response: " . substr($response, 0, 200) . "\n";
            }
            return false;
        }
        
        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON decode error for {$method} {$endpoint}\n";
            return false;
        }
        
        return $decoded;
    }
}

// Run tests
$tester = new ApiTester();
$tester->runAllTests();
