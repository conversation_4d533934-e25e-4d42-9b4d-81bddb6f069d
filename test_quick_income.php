<?php

require_once 'vendor/autoload.php';

defined('YII_DEBUG') or define('YII_DEBUG', true);
defined('YII_ENV') or define('YII_ENV', 'test');

require 'vendor/yiisoft/yii2/Yii.php';

$config = require 'config/web.php';
$app = new yii\web\Application($config);

class QuickIncomeTest
{
    private $baseUrl = 'http://localhost:8080';
    private $authToken = null;
    private $testCategoryId = null;
    private $testProductId = null;
    
    public function runTest()
    {
        echo "=== Quick Income Controller Test ===\n\n";
        
        // 1. Authenticate
        $this->authenticate();
        
        // 2. Create test data
        $this->createTestData();
        
        // 3. Test scan barcode
        $this->testScanBarcode();
        
        // 4. Test quick entry
        $this->testQuickEntry();
        
        // 5. Test search
        $this->testSearch();
        
        // 6. Cleanup
        $this->cleanup();
        
        echo "\n=== Quick Income Test Completed ===\n";
    }
    
    private function authenticate()
    {
        echo "1. Authenticating...\n";
        
        $response = $this->makeApiCall('POST', '/api/auth/login', [
            'username' => 'testuser',
            'password' => 'testpass123'
        ]);
        
        if ($response && $response['success']) {
            $this->authToken = $response['data']['token'];
            echo "✅ Authentication successful\n";
        } else {
            echo "❌ Authentication failed\n";
            exit(1);
        }
    }
    
    private function createTestData()
    {
        echo "\n2. Creating test data...\n";
        
        // Create category
        $categoryResponse = $this->makeAuthenticatedCall('POST', '/api/categories', [
            'name' => 'Test Income Category',
            'description' => 'Category for income testing'
        ]);
        
        if ($categoryResponse && $categoryResponse['success']) {
            $this->testCategoryId = $categoryResponse['data']['id'];
            echo "✅ Test category created (ID: {$this->testCategoryId})\n";
        } else {
            echo "❌ Failed to create test category\n";
        }
        
        // Create product
        $productResponse = $this->makeAuthenticatedCall('POST', '/api/products', [
            'name' => 'Test Income Product',
            'barcode' => '1111111111111',
            'category_id' => $this->testCategoryId,
            'unit_type' => 'piece',
            'price_per_unit' => 20.00,
            'current_stock' => 5
        ]);
        
        if ($productResponse && $productResponse['success']) {
            $this->testProductId = $productResponse['data']['id'];
            echo "✅ Test product created (ID: {$this->testProductId})\n";
        } else {
            echo "❌ Failed to create test product\n";
        }
    }
    
    private function testScanBarcode()
    {
        echo "\n3. Testing scan barcode...\n";
        
        $response = $this->makeAuthenticatedCall('POST', '/api/income/scan-barcode', [
            'barcode' => '1111111111111',
            'quantity' => 10,
            'price_per_unit' => 25.00,
            'notes' => 'Test income via barcode scan'
        ]);
        
        if ($response && $response['success']) {
            echo "✅ Scan barcode successful\n";
            echo "   Previous stock: {$response['data']['product']['previous_stock']}\n";
            echo "   Current stock: {$response['data']['product']['current_stock']}\n";
            echo "   Income ID: {$response['data']['income']['id']}\n";
        } else {
            echo "❌ Scan barcode failed\n";
            if ($response) {
                echo "   Error: " . ($response['error']['message'] ?? 'Unknown error') . "\n";
            }
        }
    }
    
    private function testQuickEntry()
    {
        echo "\n4. Testing quick entry...\n";
        
        $response = $this->makeAuthenticatedCall('POST', '/api/income/quick-entry', [
            'product' => [
                'id' => $this->testProductId
            ],
            'quantity' => 5,
            'price_per_unit' => 22.00,
            'notes' => 'Test income via quick entry'
        ]);
        
        if ($response && $response['success']) {
            echo "✅ Quick entry successful\n";
            echo "   Income ID: {$response['data']['income']['id']}\n";
            echo "   Was created: " . ($response['data']['was_created'] ? 'Yes' : 'No') . "\n";
        } else {
            echo "❌ Quick entry failed\n";
            if ($response) {
                echo "   Error: " . ($response['error']['message'] ?? 'Unknown error') . "\n";
            }
        }
    }
    
    private function testSearch()
    {
        echo "\n5. Testing search...\n";
        
        $response = $this->makeAuthenticatedCall('GET', '/api/income/search?q=Test&limit=5');
        
        if ($response && $response['success']) {
            echo "✅ Search successful\n";
            echo "   Found products: " . count($response['data']['products']) . "\n";
        } else {
            echo "❌ Search failed\n";
            if ($response) {
                echo "   Error: " . ($response['error']['message'] ?? 'Unknown error') . "\n";
            }
        }
    }
    
    private function cleanup()
    {
        echo "\n6. Cleaning up...\n";
        
        // Delete product
        if ($this->testProductId) {
            $this->makeAuthenticatedCall('DELETE', "/api/products/{$this->testProductId}");
            echo "✅ Test product deleted\n";
        }
        
        // Delete category
        if ($this->testCategoryId) {
            $this->makeAuthenticatedCall('DELETE', "/api/categories/{$this->testCategoryId}");
            echo "✅ Test category deleted\n";
        }
    }
    
    private function makeAuthenticatedCall($method, $endpoint, $data = null)
    {
        $headers = [];
        if ($this->authToken) {
            $headers[] = "Authorization: Bearer {$this->authToken}";
        }
        
        return $this->makeApiCall($method, $endpoint, $data, $headers);
    }
    
    private function makeApiCall($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "CURL Error: {$error}\n";
            return false;
        }
        
        if ($httpCode >= 400) {
            echo "HTTP Error {$httpCode} for {$method} {$endpoint}\n";
            return false;
        }
        
        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON decode error for {$method} {$endpoint}\n";
            return false;
        }
        
        return $decoded;
    }
}

// Run test
$test = new QuickIncomeTest();
$test->runTest();
