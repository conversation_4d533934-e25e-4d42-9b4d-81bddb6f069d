{"name": "codeception/module-asserts", "description": "Codeception module containing various assertions", "license": "MIT", "type": "library", "keywords": ["codeception", "asserts", "assertions"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "homepage": "https://medium.com/@ganieves"}], "homepage": "https://codeception.com/", "require": {"php": "^8.1", "codeception/codeception": "*@dev", "codeception/lib-asserts": "^2.0"}, "conflict": {"codeception/codeception": "<5.0"}, "minimum-stability": "dev", "autoload": {"classmap": ["src/"]}, "config": {"classmap-authoritative": true}}