# Система управления складом (Склад)

Современное PWA приложение для управления складом с поддержкой сканирования штрихкодов, построенное на PHP Yii2 Basic и PostgreSQL. Простое в использовании решение для малого и среднего бизнеса с красивым интерфейсом на Tailwind CSS! 🚀

## Возможности

- 📦 Управление товарами и категориями
- 📊 Учет прихода и продаж товаров
- 📱 Сканирование штрихкодов с помощью камеры
- ⚖️ Поддержка продаж по весу и объему
- 🏷️ Автоматическая генерация штрихкодов
- 📈 Отчеты и аналитика
- 📱 PWA - работает как мобильное приложение
- 🔄 Офлайн поддержка

## Содержание

- [Структура проекта](#структура-проекта)
- [Требования](#требования)
- [Установка](#установка)
- [Использование](#использование)
- [Конфигурация](#конфигурация)
- [Дизайн и интерфейс](#дизайн-и-интерфейс)
- [Технические детали](#технические-детали)
- [Тестирование](#тестирование)
- [Лицензия](#лицензия)
- [Поддержка](#поддержка)

## Структура проекта

```
assets/             содержит определения ресурсов
commands/           содержит консольные команды
config/             содержит конфигурации приложения
controllers/        содержит контроллеры веб-приложения
migrations/         содержит миграции базы данных
models/             содержит модели данных
runtime/            содержит файлы, генерируемые во время выполнения
views/              содержит представления веб-приложения
web/                содержит входной скрипт и веб-ресурсы
```

Подробнее о структуре проекта Yii2 можно узнать в [официальной документации](https://www.yiiframework.com/doc/guide/2.0/en/structure-overview).

## Требования

- PHP 8.2 или выше
- PostgreSQL 12 или выше
- Composer
- Расширения PHP: pgsql, gd, intl, mbstring, openssl

## Установка

### 1. Клонирование репозитория

```bash
git clone https://github.com/akramjon-2002/sklad.git
cd sklad
```

### 2. Установка зависимостей

```bash
composer install
```

### 3. Настройка базы данных

Создайте базу данных PostgreSQL и настройте подключение в файле [`config/db.php`](config/db.php):

```php
return [
    'class' => 'yii\db\Connection',
    'dsn' => 'pgsql:host=localhost;dbname=sklad',
    'username' => 'postgres',
    'password' => 'your_password',
    'charset' => 'utf8',
];
```

### 4. Применение миграций

```bash
php yii migrate
```

### 5. Настройка веб-сервера

Настройте веб-сервер так, чтобы корневая директория указывала на папку [`web/`](web/).

Для разработки можно использовать встроенный сервер PHP:

```bash
php yii serve --port=8080
```

Приложение будет доступно по адресу: `http://localhost:8080`


## Использование

### Основные функции

1. **Панель управления** - главная страница с общей статистикой и быстрыми действиями ([ReportController](controllers/ReportController.php), [dashboard view](views/report/index.php))
2. **Товары** - управление каталогом товаров, категориями, штрихкодами ([ProductController](controllers/ProductController.php), [CategoryController](controllers/CategoryController.php))
3. **Быстрая продажа** - сканирование штрихкодов и оформление продаж ([SaleController](controllers/SaleController.php), [quick sale view](views/sale/quick.php))
4. **Приход товаров** - учет поступления товаров на склад ([IncomeController](controllers/IncomeController.php))
5. **Отчеты** - аналитика продаж, остатков, движения товаров

Все функции доступны через удобный веб-интерфейс с современным дизайном, оптимизированный для работы на мобильных устройствах.

### Сканирование штрихкодов

Приложение поддерживает удобное сканирование штрихкодов через камеру устройства:
- Откройте раздел ["Быстрая продажа"](views/sale/quick.php)
- Нажмите кнопку "Включить камеру" 📷
- Наведите камеру на штрихкод товара
- Информация о товаре автоматически загрузится в форму продажи ✨

Поддерживаются все основные форматы штрихкодов: EAN-13, EAN-8, Code 128, Code 39 и многие другие.

### PWA функции

Приложение работает как полноценное PWA (Progressive Web App):
- 📱 Установка на главный экран мобильного устройства
- 🔄 Работа в офлайн режиме благодаря [Service Worker](web/sw.js)
- 🎨 Современный дизайн с [Tailwind CSS](web/css/tailwind.css) и [CSS переменными](web/css/modern.css)
- ⚡ [PWA манифест](web/manifest.json) с быстрыми ярлыками и иконками
- 📱 Адаптивный интерфейс с мобильным меню и touch-friendly элементами
- 🔔 Push-уведомления (планируется в следующих версиях)

## Конфигурация

### База данных

Настройте подключение к PostgreSQL в файле [`config/db.php`](config/db.php).

### Дополнительные настройки

Для тонкой настройки приложения под ваши потребности, проверьте и отредактируйте файлы в директории [`config/`](config/):

- [`config/web.php`](config/web.php) - основная конфигурация веб-приложения
- [`config/console.php`](config/console.php) - конфигурация консольных команд  
- [`config/params.php`](config/params.php) - параметры приложения
- [`config/test.php`](config/test.php) - настройки для тестирования

## Дизайн и интерфейс

### Современный UI/UX

Приложение использует современный подход к дизайну с акцентом на удобство использования:

- 🎨 **Tailwind CSS** - utility-first подход для быстрой и гибкой стилизации
- 📱 **Адаптивный дизайн** - идеально работает на всех устройствах от смартфонов до десктопов
- 🌈 **Градиенты и анимации** - современные визуальные эффекты для лучшего пользовательского опыта
- 🎯 **Интуитивная навигация** - понятные иконки и логичная структура меню
- ⚡ **Быстрые действия** - карточки с основными функциями на главной странице

### Мобильная оптимизация

- 📱 Мобильное меню с плавными анимациями
- 👆 Touch-friendly элементы интерфейса
- 🔄 Swipe-жесты для навигации
- 📷 Оптимизированный интерфейс сканера штрихкодов

## Технические детали

### Архитектура

Приложение построено на фреймворке Yii2 с использованием паттерна MVC:
- **Модели** - работа с данными и бизнес-логика
- **Представления** - пользовательский интерфейс
- **Контроллеры** - обработка запросов и координация

### База данных

Используется PostgreSQL с следующими основными таблицами:
- `category` - категории товаров ([Category.php](models/Category.php))
- `product` - товары ([Product.php](models/Product.php))
- `income` - приход товаров ([Income.php](models/Income.php))
- `sale` - продажи ([Sale.php](models/Sale.php))

Схема базы данных создается автоматически при применении [миграций](migrations/).

### Зависимости

- **yiisoft/yii2-app-basic** - базовый шаблон Yii2 Framework
- **picqer/php-barcode-generator** - генерация штрихкодов (подробности в [composer.json](composer.json))
- **QuaggaJS** - JavaScript библиотека для сканирования штрихкодов в браузере
- **Bootstrap 5** - CSS фреймворк для базовых компонентов
- **Tailwind CSS** - современный utility-first CSS фреймворк для стилизации ([tailwind.css](web/css/tailwind.css))

## Тестирование

Тесты расположены в директории [`tests`](tests/) и разработаны с использованием [Codeception PHP Testing Framework](https://codeception.com/).

Запуск тестов:

```bash
vendor/bin/codecept run
```

> **Примечание**: Для полноценного тестирования убедитесь, что настроена тестовая база данных в [`config/test_db.php`](config/test_db.php).

## Лицензия

Этот проект распространяется под лицензией MIT. См. файл [LICENSE](LICENSE) для получения дополнительной информации.

## Поддержка

Если у вас есть вопросы, предложения или вы столкнулись с проблемами, не стесняйтесь создать [issue в репозитории GitHub](https://github.com/akramjon-2002/sklad/issues). Мы будем рады помочь вам с настройкой и использованием системы! 💬

### Полезные ссылки

- [Документация Yii2](https://www.yiiframework.com/doc/guide/2.0/ru)
- [PostgreSQL документация](https://www.postgresql.org/docs/)
- [PWA руководство](https://web.dev/progressive-web-apps/)
- [Tailwind CSS документация](https://tailwindcss.com/docs)

---

**Спасибо за использование нашей системы управления складом!** 🚀

Мы постоянно работаем над улучшением функциональности и будем благодарны за ваши отзывы и предложения. Ваше мнение помогает нам делать систему еще лучше! ✨
