<?php

namespace app\controllers\api;

use Yii;
use yii\rest\Controller;
use yii\filters\ContentNegotiator;
use yii\filters\VerbFilter;
use yii\filters\Cors;
use yii\web\Response;

/**
 * Base API controller
 */
class BaseController extends Controller
{
    /**
     * @inheritdoc
     */
    public function behaviors()
    {
        return [
            'contentNegotiator' => [
                'class' => ContentNegotiator::class,
                'formats' => [
                    'application/json' => Response::FORMAT_JSON,
                ],
            ],
            'verbFilter' => [
                'class' => VerbFilter::class,
                'actions' => $this->verbs(),
            ],
            'cors' => [
                'class' => Cors::class,
                'cors' => [
                    'Origin' => ['*'],
                    'Access-Control-Request-Method' => ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'HEAD', 'OPTIONS'],
                    'Access-Control-Request-Headers' => ['*'],
                    'Access-Control-Allow-Credentials' => true,
                    'Access-Control-Max-Age' => 86400,
                ],
            ],
        ];
    }

    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [];
    }

    /**
     * Формирует успешный ответ
     *
     * @param mixed $data Данные для ответа
     * @param int $statusCode HTTP-код статуса
     * @return array
     */
    protected function successResponse($data = [], $statusCode = 200)
    {
        Yii::$app->response->statusCode = $statusCode;
        return [
            'success' => true,
            'data' => $data,
            'error' => null,
        ];
    }

    /**
     * Формирует ответ с ошибкой
     *
     * @param string $message Сообщение об ошибке
     * @param int $statusCode HTTP-код статуса
     * @param array $details Дополнительные данные об ошибке
     * @return array
     */
    protected function errorResponse($message, $statusCode = 400, $details = [])
    {
        Yii::$app->response->statusCode = $statusCode;
        return [
            'success' => false,
            'data' => null,
            'error' => [
                'message' => $message,
                'details' => $details,
            ],
        ];
    }

    /**
     * Creates a standardized ActiveDataProvider with pagination and sorting
     *
     * @param \yii\db\ActiveQuery $query The query to create data provider from
     * @param array $defaultOrder Default sort order
     * @param int $pageSize Default page size
     * @return \yii\data\ActiveDataProvider
     */
    protected function createDataProvider($query, $defaultOrder = null, $pageSize = 20)
    {
        $request = Yii::$app->request;
        
        // Get pagination parameters
        $page = (int)$request->get('page', 1);
        $limit = (int)$request->get('limit', $pageSize);
        
        // Sanitize limit to prevent excessive queries
        if ($limit > 100) {
            $limit = 100;
        }
        
        // Create data provider
        $dataProvider = new \yii\data\ActiveDataProvider([
            'query' => $query,
            'pagination' => [
                'pageSize' => $limit,
                'page' => $page - 1, // adjust because page is 0-based in Yii
            ],
            'sort' => [
                'defaultOrder' => $defaultOrder ?: [],
            ],
        ]);
        
        return $dataProvider;
    }

    /**
     * Формирует успешный ответ с пагинацией
     *
     * @param \yii\data\ActiveDataProvider $dataProvider Data provider
     * @param int $statusCode HTTP-код статуса
     * @return array
     */
    protected function paginatedResponse($dataProvider, $statusCode = 200)
    {
        Yii::$app->response->statusCode = $statusCode;
        $pagination = $dataProvider->getPagination();
        
        return [
            'success' => true,
            'data' => $dataProvider->getModels(),
            'error' => null,
            '_meta' => [
                'totalCount' => $dataProvider->getTotalCount(),
                'pageCount' => $pagination->pageCount,
                'currentPage' => $pagination->page + 1, // adjust because page is 0-based in Yii
                'perPage' => $pagination->pageSize,
            ],
        ];
    }
}
