<?php

namespace app\commands;

use Yii;
use app\models\Product;
use app\models\Sale;
use app\models\Income;
use app\models\Category;
use yii\console\Controller;

/**
 * Demo controller to showcase system functionality
 */
class DemoController extends Controller
{
    /**
     * Demonstrate system functionality
     */
    public function actionRun()
    {
        $this->stdout("🏪 ДЕМОНСТРАЦИЯ СИСТЕМЫ УПРАВЛЕНИЯ СКЛАДОМ\n");
        $this->stdout("=" . str_repeat("=", 50) . "\n\n");

        // 1. Show categories
        $this->stdout("📁 КАТЕГОРИИ ТОВАРОВ:\n");
        $categories = Category::find()->all();
        foreach ($categories as $category) {
            $productCount = $category->getProducts()->count();
            $this->stdout("  • {$category->name} ({$productCount} товаров)\n");
        }
        $this->stdout("\n");

        // 2. Show products with stock
        $this->stdout("📦 ТОВАРЫ НА СКЛАДЕ:\n");
        $products = Product::find()->with('category')->all();
        foreach ($products as $product) {
            $categoryName = $product->category ? $product->category->name : 'Без категории';
            $this->stdout(sprintf(
                "  • %s [%s] - %s %.2f₽ (В наличии: %.3f %s)\n",
                $product->name,
                $product->barcode,
                $categoryName,
                $product->price_per_unit,
                $product->current_stock,
                $product->getUnitTypeLabel()
            ));
        }
        $this->stdout("\n");

        // 3. Simulate some sales
        $this->stdout("💰 СИМУЛЯЦИЯ ПРОДАЖ:\n");
        $this->simulateSales();
        $this->stdout("\n");

        // 4. Show sales statistics
        $this->stdout("📊 СТАТИСТИКА ПРОДАЖ:\n");
        $this->showSalesStats();
        $this->stdout("\n");

        // 5. Show stock levels
        $this->stdout("⚠️ КОНТРОЛЬ ОСТАТКОВ:\n");
        $this->showStockLevels();

        $this->stdout("\n✅ Демонстрация завершена!\n");
        $this->stdout("🌐 Откройте http://localhost:8080 для работы с веб-интерфейсом\n");
    }

    private function simulateSales()
    {
        $products = Product::find()->where(['>', 'current_stock', 0])->limit(5)->all();
        
        foreach ($products as $product) {
            $quantity = min(rand(1, 3), $product->current_stock);
            if ($quantity > 0) {
                $sale = new Sale();
                $sale->product_id = $product->id;
                $sale->quantity = $quantity;
                $sale->price_per_unit = $product->price_per_unit;
                $sale->total_amount = $quantity * $product->price_per_unit;
                $sale->notes = 'Демо продажа';
                
                if ($sale->save()) {
                    $this->stdout("  ✓ Продано: {$product->name} x{$quantity} = {$sale->total_amount}₽\n");
                }
            }
        }
    }

    private function showSalesStats()
    {
        $today = date('Y-m-d');
        
        $todaySales = Sale::find()
            ->where(['>=', 'created_at', $today . ' 00:00:00'])
            ->sum('total_amount') ?: 0;
            
        $todayCount = Sale::find()
            ->where(['>=', 'created_at', $today . ' 00:00:00'])
            ->count();

        $totalSales = Sale::find()->sum('total_amount') ?: 0;
        $totalCount = Sale::find()->count();

        $this->stdout("  • Продаж сегодня: {$todayCount} на сумму {$todaySales}₽\n");
        $this->stdout("  • Всего продаж: {$totalCount} на сумму {$totalSales}₽\n");
    }

    private function showStockLevels()
    {
        $lowStock = Product::find()->where(['<=', 'current_stock', 10])->andWhere(['>', 'current_stock', 0])->all();
        $outOfStock = Product::find()->where(['current_stock' => 0])->count();

        if ($lowStock) {
            $this->stdout("  📉 Товары с низким остатком:\n");
            foreach ($lowStock as $product) {
                $this->stdout("    • {$product->name}: {$product->current_stock} {$product->getUnitTypeLabel()}\n");
            }
        }

        if ($outOfStock > 0) {
            $this->stdout("  🚫 Товаров без остатка: {$outOfStock}\n");
        }

        if (!$lowStock && $outOfStock == 0) {
            $this->stdout("  ✅ Все товары в достаточном количестве\n");
        }
    }

    /**
     * Test barcode scanning simulation
     */
    public function actionTestBarcode($barcode = '4607177411243')
    {
        $this->stdout("🔍 ТЕСТ СКАНИРОВАНИЯ ШТРИХКОДА: {$barcode}\n");
        $this->stdout(str_repeat("-", 50) . "\n");

        $product = Product::find()->where(['barcode' => $barcode])->one();
        
        if ($product) {
            $this->stdout("✅ Товар найден:\n");
            $this->stdout("  • Название: {$product->name}\n");
            $this->stdout("  • Категория: " . ($product->category ? $product->category->name : 'Не указана') . "\n");
            $this->stdout("  • Цена: {$product->price_per_unit}₽\n");
            $this->stdout("  • Остаток: {$product->current_stock} {$product->getUnitTypeLabel()}\n");
            
            if ($product->current_stock > 0) {
                $this->stdout("\n💡 Товар доступен для продажи!\n");
            } else {
                $this->stdout("\n⚠️ Товар отсутствует на складе!\n");
            }
        } else {
            $this->stdout("❌ Товар с штрихкодом {$barcode} не найден\n");
        }
    }
}
