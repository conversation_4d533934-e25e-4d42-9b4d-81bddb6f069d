actor: Tester
bootstrap: _bootstrap.php
paths:
    tests: tests
    output: tests/_output
    data: tests/_data
    helpers: tests/_support
settings:
    memory_limit: 1024M
    colors: true
modules:
    config:
        Yii2:
            configFile: 'config/test.php'

# To enable code coverage:
#coverage:
#    #c3_url: http://localhost:8080/index-test.php/
#    enabled: true
#    #remote: true
#    #remote_config: '../codeception.yml'
#    whitelist:
#        include:
#            - models/*
#            - controllers/*
#            - commands/*
#            - mail/*
