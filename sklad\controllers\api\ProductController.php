<?php

namespace app\controllers\api;

use Yii;
use app\models\Product;
use app\models\Category;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;

/**
 * ProductController implements the API endpoints for Product model
 */
class ProductController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'index' => ['GET'],
            'view' => ['GET'],
            'create' => ['POST'],
            'update' => ['PUT', 'PATCH'],
            'delete' => ['DELETE'],
        ];
    }    /**
     * Lists all products with filtering, sorting and pagination
     * @return array
     */
    public function actionIndex()
    {
        $query = Product::find();
        $request = Yii::$app->request;
        
        // Apply filters
        if ($category = $request->get('category')) {
            $query->andWhere(['category_id' => $category]);
        }
        
        if ($search = $request->get('search')) {
            $query->andWhere(['or', 
                ['like', 'name', $search],
                ['like', 'sku', $search],
                ['like', 'description', $search]
            ]);
        }
        
        if ($minStock = $request->get('min_stock')) {
            $query->andWhere(['>=', 'current_stock', $minStock]);
        }
        
        if ($maxStock = $request->get('max_stock')) {
            $query->andWhere(['<=', 'current_stock', $maxStock]);
        }
        
        // Create data provider with default sorting
        $dataProvider = $this->createDataProvider($query, ['id' => SORT_DESC]);
        
        // Return paginated response
        return $this->paginatedResponse($dataProvider);
    }

    /**
     * Displays a specific product
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->successResponse($model);
    }

    /**
     * Creates a new product
     * @return array
     */
    public function actionCreate()
    {
        $model = new Product();
        
        if ($model->load(Yii::$app->request->post(), '') && $model->save()) {
            return $this->successResponse($model, 201);
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Updates an existing product
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        
        if ($model->load(Yii::$app->request->post(), '') && $model->save()) {
            return $this->successResponse($model);
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Deletes a product
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            return $this->successResponse(null, 204);
        }
        
        return $this->errorResponse('Не удалось удалить товар', 500);
    }

    /**
     * Finds the Product model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id
     * @return Product the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Product::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('Товар не найден');
    }
}
