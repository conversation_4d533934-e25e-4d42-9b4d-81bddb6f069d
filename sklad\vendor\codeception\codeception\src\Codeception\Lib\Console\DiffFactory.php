<?php

declare(strict_types=1);

namespace Codeception\Lib\Console;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Comparator\ComparisonFailure;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Diff\Differ;
use <PERSON><PERSON><PERSON><PERSON>n\Diff\Output\UnifiedDiffOutputBuilder;

class DiffFactory
{
    public function createDiff(ComparisonFailure $failure): string
    {
        return $this->getDiff($failure->getExpectedAsString(), $failure->getActualAsString());
    }

    private function getDiff(string $expected = '', string $actual = ''): string
    {
        $differ = new Differ(new UnifiedDiffOutputBuilder(''));
        return ($expected || $actual) ? $differ->diff($expected, $actual) : '';
    }
}
