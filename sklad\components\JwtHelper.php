<?php

namespace app\components;

use Yii;
use yii\base\Component;
use app\models\User;

/**
 * Simple JWT implementation for API authentication
 */
class JwtHelper extends Component
{
    /**
     * Generate JWT token for user
     * 
     * @param User $user User model
     * @return string JWT token
     */
    public static function generateToken($user)
    {
        $time = time();
        
        $payload = [
            'iat' => $time,                       // Issued at
            'exp' => $time + 3600 * 24 * 7,       // Expires in 7 days
            'uid' => $user->id,                   // User ID
            'username' => $user->username,        // Username
        ];
        
        // Generate JWT token
        $jwt = self::base64UrlEncode(json_encode(['alg' => 'HS256', 'typ' => 'JWT'])) . '.' .
               self::base64UrlEncode(json_encode($payload));
        
        // Generate signature
        $signature = hash_hmac('sha256', $jwt, Yii::$app->params['jwtSecret'], true);
        $jwt .= '.' . self::base64UrlEncode($signature);
        
        return $jwt;
    }
    
    /**
     * Parse and validate JWT token
     * 
     * @param string $token JWT token
     * @return array|null Payload data or null if token is invalid
     */
    public static function parseToken($token)
    {
        $parts = explode('.', $token);
        
        if (count($parts) !== 3) {
            return null;
        }
        
        $header = json_decode(self::base64UrlDecode($parts[0]), true);
        $payload = json_decode(self::base64UrlDecode($parts[1]), true);
        $signature = self::base64UrlDecode($parts[2]);
        
        if (!$header || !$payload) {
            return null;
        }
        
        // Verify signature
        $signatureCheck = hash_hmac('sha256', $parts[0] . '.' . $parts[1], Yii::$app->params['jwtSecret'], true);
        
        if ($signature !== $signatureCheck) {
            return null;
        }
        
        // Check expiration
        if (isset($payload['exp']) && $payload['exp'] < time()) {
            return null;
        }
        
        return $payload;
    }
    
    /**
     * Base64 URL encode
     * 
     * @param string $data Data to encode
     * @return string Encoded data
     */
    private static function base64UrlEncode($data)
    {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Base64 URL decode
     * 
     * @param string $data Data to decode
     * @return string Decoded data
     */
    private static function base64UrlDecode($data)
    {
        return base64_decode(strtr($data, '-_', '+/') . str_repeat('=', 3 - (3 + strlen($data)) % 4));
    }
}
