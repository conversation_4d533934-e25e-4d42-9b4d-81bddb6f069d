<?php

namespace app\commands;

use Yii;
use yii\console\Controller;
use yii\helpers\Console;
use tests\SupabaseTestRunner;

/**
 * Test Controller for running API tests
 */
class TestController extends Controller
{
    /**
     * Run full test suite with Supabase
     */
    public function actionSupabase()
    {
        $runner = new SupabaseTestRunner();
        $result = $runner->runFullTestSuite();
        
        return $result ? 0 : 1;
    }

    /**
     * Test database connection only
     */
    public function actionConnection()
    {
        $runner = new SupabaseTestRunner();
        
        if (!$runner->isSupabaseConfigured()) {
            Console::output("❌ Supabase not configured", Console::FG_RED);
            return 1;
        }
        
        $result = $runner->testConnection();
        return $result ? 0 : 1;
    }

    /**
     * Run migrations only
     */
    public function actionMigrate()
    {
        $runner = new SupabaseTestRunner();
        
        if (!$runner->isSupabaseConfigured()) {
            Console::output("❌ Supabase not configured", Console::FG_RED);
            return 1;
        }
        
        $result = $runner->runMigrations();
        return $result ? 0 : 1;
    }

    /**
     * Test API performance
     */
    public function actionPerformance()
    {
        $runner = new SupabaseTestRunner();
        $runner->testPerformance();
        return 0;
    }

    /**
     * Create test data
     */
    public function actionCreateData()
    {
        $runner = new SupabaseTestRunner();
        
        if (!$runner->isSupabaseConfigured()) {
            Console::output("❌ Supabase not configured", Console::FG_RED);
            return 1;
        }
        
        $result = $runner->createTestData();
        return $result ? 0 : 1;
    }

    /**
     * Cleanup test data
     */
    public function actionCleanup()
    {
        $runner = new SupabaseTestRunner();
        $runner->cleanup();
        return 0;
    }

    /**
     * Show help for test commands
     */
    public function actionHelp()
    {
        Console::output("Warehouse API Test Commands:");
        Console::output("");
        Console::output("php yii test/supabase     - Run full test suite with Supabase");
        Console::output("php yii test/connection   - Test database connection");
        Console::output("php yii test/migrate      - Run database migrations");
        Console::output("php yii test/performance  - Test API performance");
        Console::output("php yii test/create-data  - Create test data");
        Console::output("php yii test/cleanup      - Cleanup test data");
        Console::output("");
        Console::output("Environment variables needed for Supabase:");
        Console::output("SUPABASE_DB_URL or individual parameters:");
        Console::output("SUPABASE_DB_HOST, SUPABASE_DB_NAME, SUPABASE_DB_USER, SUPABASE_DB_PASS");
        
        return 0;
    }
}
