<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\components\JwtHelper;

class QuickOperationsApiCest
{
    private $authToken;
    private $testProduct;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = User::findByUsername('admin');
        if ($user) {
            $this->authToken = JwtHelper::generateToken($user);
        }

        // Create test category
        $category = new Category();
        $category->name = 'Test Category';
        $category->save();

        // Create test product
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $category->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 10;
        $this->testProduct->save();
    }

    public function testApiInfo(ApiTester $I)
    {
        $I->sendGET('/api');
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Warehouse Management API',
                'status' => 'operational'
            ]
        ]);
    }

    public function testAuthenticationRequired(ApiTester $I)
    {
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        $I->seeResponseCodeIs(401);
    }

    public function testQuickIncomeByBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 5,
            'price_per_unit' => 30.00,
            'notes' => 'Test income'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'income' => [
                    'quantity' => 5,
                    'price_per_unit' => 30.00,
                    'notes' => 'Test income'
                ],
                'product' => [
                    'barcode' => '1234567890123',
                    'previous_stock' => 10,
                    'current_stock' => 15
                ]
            ]
        ]);
    }

    public function testQuickSaleByBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 2,
            'price_per_unit' => 35.99,
            'notes' => 'Test sale'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 2,
                    'price_per_unit' => 35.99,
                    'notes' => 'Test sale'
                ],
                'product' => [
                    'barcode' => '1234567890123'
                ]
            ]
        ]);
    }

    public function testInsufficientStock(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 100, // More than available
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Insufficient stock'
            ]
        ]);
    }

    public function testProductSearch(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/income/search?q=Test&limit=10');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'query' => 'Test'
            ]
        ]);
    }

    public function testBatchIncome(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/batch', [
            'items' => [
                [
                    'product_id' => $this->testProduct->id,
                    'quantity' => 3,
                    'price_per_unit' => 28.00,
                    'notes' => 'Batch item 1'
                ]
            ]
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'summary' => [
                    'total' => 1,
                    'success' => 1,
                    'errors' => 0
                ]
            ]
        ]);
    }

    public function testInvalidBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => 'invalid',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid barcode format'
            ]
        ]);
    }

    public function testProductNotFound(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '9999999999999',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Product not found'
            ]
        ]);
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testProduct) {
            $this->testProduct->delete();
        }
    }
}
