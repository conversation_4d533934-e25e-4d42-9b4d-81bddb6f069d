<?php

namespace app\components;

use Yii;
use yii\web\ErrorHandler as YiiErrorHandler;
use yii\web\Response;
use yii\web\HttpException;
use yii\base\Exception as BaseException;
use yii\base\ErrorException;

/**
 * API error handler for formatting errors as JSON
 */
class ApiErrorHandler extends YiiErrorHandler
{
    /**
     * @inheritdoc
     */
    protected function renderException($exception)
    {
        // Check if this is an API request based on URL or accept header
        $request = Yii::$app->request;
        $isApiRequest = (
            strpos($request->getUrl(), '/api/') !== false || 
            strpos($request->headers->get('accept'), 'application/json') !== false
        );
        
        // For API requests, handle as JSON error response
        if ($isApiRequest) {
            // Clear any previous output that might corrupt our JSON response
            if (ob_get_level()) {
                ob_clean();
            }
            
            $response = Yii::$app->response;
            $response->format = Response::FORMAT_JSON;
            
            // Determine HTTP status code
            if ($exception instanceof HttpException) {
                $statusCode = $exception->statusCode;
            } else {
                $statusCode = 500;
            }
            
            $response->statusCode = $statusCode;
            
            // Prepare error response
            $errorDetail = YII_DEBUG ? [
                'type' => get_class($exception),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'stack' => explode("\n", $exception->getTraceAsString()),
            ] : null;
            
            $response->data = [
                'success' => false,
                'data' => null,
                'error' => [
                    'message' => $exception->getMessage(),
                    'code' => $exception->getCode(),
                    'details' => $errorDetail,
                ],
            ];
            
            $response->send();
            return;
        }
        
        // For non-API requests, use the default error handling
        parent::renderException($exception);
    }
}
