<?php

namespace app\controllers\api;

use Yii;
use app\models\Product;
use app\models\Sale;
use app\models\Income;
use yii\helpers\ArrayHelper;
use yii\db\Expression;

/**
 * ReportController implements API endpoints for various warehouse reports
 */
class ReportController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'stock' => ['GET'],
            'sales' => ['GET'],
            'income' => ['GET'],
            'summary' => ['GET'],
        ];
    }

    /**
     * Get stock report - current stock levels of all products
     * @return array
     */
    public function actionStock()
    {
        $products = Product::find()
            ->with('category')
            ->orderBy(['name' => SORT_ASC])
            ->all();
            
        $data = ArrayHelper::toArray($products, [
            Product::class => [
                'id',
                'name',
                'sku',
                'current_stock',
                'unit',
                'price',
                'category_name' => function ($model) {
                    return $model->category ? $model->category->name : null;
                },
            ],
        ]);
        
        return $this->successResponse($data);
    }

    /**
     * Get sales report by date range
     * @param string $start Start date (Y-m-d)
     * @param string $end End date (Y-m-d)
     * @return array
     */
    public function actionSales($start = null, $end = null)
    {
        $query = Sale::find()->with('product');
        
        if ($start !== null) {
            $query->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        
        if ($end !== null) {
            $query->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        
        $sales = $query->orderBy(['created_at' => SORT_DESC])->all();
        
        $data = ArrayHelper::toArray($sales, [
            Sale::class => [
                'id',
                'quantity',
                'price_per_unit',
                'total_amount',
                'notes',
                'created_at',
                'product_name' => function ($model) {
                    return $model->product ? $model->product->name : null;
                },
                'product_sku' => function ($model) {
                    return $model->product ? $model->product->sku : null;
                },
            ],
        ]);
        
        return $this->successResponse($data);
    }

    /**
     * Get income report by date range
     * @param string $start Start date (Y-m-d)
     * @param string $end End date (Y-m-d)
     * @return array
     */
    public function actionIncome($start = null, $end = null)
    {
        $query = Income::find()->with('product');
        
        if ($start !== null) {
            $query->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        
        if ($end !== null) {
            $query->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        
        $incomes = $query->orderBy(['created_at' => SORT_DESC])->all();
        
        $data = ArrayHelper::toArray($incomes, [
            Income::class => [
                'id',
                'quantity',
                'price_per_unit',
                'total_cost',
                'notes',
                'created_at',
                'product_name' => function ($model) {
                    return $model->product ? $model->product->name : null;
                },
                'product_sku' => function ($model) {
                    return $model->product ? $model->product->sku : null;
                },
            ],
        ]);
        
        return $this->successResponse($data);
    }

    /**
     * Get summary report with statistics
     * @param string $start Start date (Y-m-d)
     * @param string $end End date (Y-m-d)
     * @return array
     */
    public function actionSummary($start = null, $end = null)
    {
        // Default to current month if no dates provided
        if ($start === null && $end === null) {
            $start = date('Y-m-01'); // First day of current month
            $end = date('Y-m-t');    // Last day of current month
        }
        
        // Total sales
        $salesQuery = Sale::find();
        if ($start !== null) {
            $salesQuery->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        if ($end !== null) {
            $salesQuery->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        $totalSales = $salesQuery->sum('total_amount') ?: 0;
        $salesCount = $salesQuery->count();
        
        // Total income/purchases
        $incomeQuery = Income::find();
        if ($start !== null) {
            $incomeQuery->andWhere(['>=', 'DATE(created_at)', $start]);
        }
        if ($end !== null) {
            $incomeQuery->andWhere(['<=', 'DATE(created_at)', $end]);
        }
        $totalIncome = $incomeQuery->sum('total_cost') ?: 0;
        $incomeCount = $incomeQuery->count();
        
        // Profit calculation
        $profit = $totalSales - $totalIncome;
        
        // Current inventory value
        $inventoryValue = Yii::$app->db->createCommand('
            SELECT SUM(current_stock * price) as value FROM product
        ')->queryScalar() ?: 0;
        
        // Product count
        $productsCount = Product::find()->count();
        
        $data = [
            'period' => [
                'start' => $start,
                'end' => $end,
            ],
            'sales' => [
                'total' => (float)$totalSales,
                'count' => (int)$salesCount,
            ],
            'purchases' => [
                'total' => (float)$totalIncome,
                'count' => (int)$incomeCount,
            ],
            'profit' => (float)$profit,
            'inventory' => [
                'value' => (float)$inventoryValue,
                'products_count' => (int)$productsCount,
            ],
        ];
        
        return $this->successResponse($data);
    }
}
