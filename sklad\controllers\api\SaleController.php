<?php

namespace app\controllers\api;

use Yii;
use app\models\Sale;
use app\models\Product;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;
use yii\web\BadRequestHttpException;

/**
 * SaleController implements the API endpoints for Sale model
 */
class SaleController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'index' => ['GET'],
            'view' => ['GET'],
            'create' => ['POST'],
            'delete' => ['DELETE'],
        ];
    }    /**
     * Lists all sales with filtering, sorting and pagination
     * @return array
     */
    public function actionIndex()
    {
        $query = Sale::find()->with('product');
        $request = Yii::$app->request;
        
        // Apply date range filters
        if ($startDate = $request->get('start_date')) {
            $query->andWhere(['>=', 'DATE(created_at)', $startDate]);
        }
        
        if ($endDate = $request->get('end_date')) {
            $query->andWhere(['<=', 'DATE(created_at)', $endDate]);
        }
        
        // Apply product filter
        if ($productId = $request->get('product_id')) {
            $query->andWhere(['product_id' => $productId]);
        }
        
        // Apply amount range filters
        if ($minAmount = $request->get('min_amount')) {
            $query->andWhere(['>=', 'total_amount', $minAmount]);
        }
        
        if ($maxAmount = $request->get('max_amount')) {
            $query->andWhere(['<=', 'total_amount', $maxAmount]);
        }
        
        // Create data provider with default sorting
        $dataProvider = $this->createDataProvider($query, ['created_at' => SORT_DESC]);
        
        // Return paginated response
        return $this->paginatedResponse($dataProvider);
    }

    /**
     * Displays a specific sale
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->successResponse($model);
    }

    /**
     * Creates a new sale
     * @return array
     */
    public function actionCreate()
    {
        $model = new Sale();
        
        if ($model->load(Yii::$app->request->post(), '')) {
            // Check product existence
            $product = Product::findOne($model->product_id);
            if (!$product) {
                return $this->errorResponse('Товар не найден', 404);
            }
            
            // Check if there is enough stock
            if (!$product->hasEnoughStock($model->quantity)) {
                return $this->errorResponse(
                    'Недостаточно товара на складе',
                    400,
                    ['available' => $product->current_stock]
                );
            }
            
            if ($model->save()) {
                // Return the sale with related product data
                $result = $model->toArray();
                $result['product'] = $model->product->toArray();
                
                return $this->successResponse($result, 201);
            }
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Deletes a sale record
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        // Note: In a real-world app, you might want to prevent deletion of sales 
        // or implement a reversal of stock changes when a sale is deleted
        $model = $this->findModel($id);
        
        if ($model->delete()) {
            return $this->successResponse(null, 204);
        }
        
        return $this->errorResponse('Не удалось удалить продажу', 500);
    }

    /**
     * Finds the Sale model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id
     * @return Sale the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Sale::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('Продажа не найдена');
    }
}
