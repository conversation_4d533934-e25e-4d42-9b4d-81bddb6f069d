<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\models\Sale;
use app\models\Income;
use app\components\JwtHelper;

class ReportControllerCest
{
    private $authToken;
    private $testCategory;
    private $testProduct;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->save();

        // Create test product
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $this->testCategory->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 20;
        $this->testProduct->save();

        // Create test data for reports
        $this->createTestData();
    }

    private function createTestData()
    {
        // Create some income records
        $income1 = new Income();
        $income1->product_id = $this->testProduct->id;
        $income1->quantity = 10;
        $income1->price_per_unit = 20.00;
        $income1->notes = 'Test income 1';
        $income1->save();

        $income2 = new Income();
        $income2->product_id = $this->testProduct->id;
        $income2->quantity = 5;
        $income2->price_per_unit = 22.00;
        $income2->notes = 'Test income 2';
        $income2->save();

        // Create some sale records
        $sale1 = new Sale();
        $sale1->product_id = $this->testProduct->id;
        $sale1->quantity = 3;
        $sale1->price_per_unit = 30.00;
        $sale1->notes = 'Test sale 1';
        $sale1->save();

        $sale2 = new Sale();
        $sale2->product_id = $this->testProduct->id;
        $sale2->quantity = 2;
        $sale2->price_per_unit = 32.00;
        $sale2->notes = 'Test sale 2';
        $sale2->save();
    }

    public function testGetStockReportWithAuth(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/stock');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'stock'
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.products');
        $I->seeResponseJsonMatchesJsonPath('$.data.summary');
    }

    public function testGetStockReportWithoutAuth(ApiTester $I)
    {
        $I->sendGET('/api/reports/stock');
        
        $I->seeResponseCodeIs(401);
    }

    public function testGetStockReportWithFilters(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/stock?category_id=' . $this->testCategory->id . '&low_stock=10&limit=20');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'stock',
                'filters' => [
                    'category_id' => $this->testCategory->id,
                    'low_stock' => 10
                ]
            ]
        ]);
    }

    public function testGetSalesReport(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/sales?start_date=2025-01-01&end_date=2025-12-31');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'sales',
                'period' => [
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-12-31'
                ]
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.sales');
        $I->seeResponseJsonMatchesJsonPath('$.data.summary');
    }

    public function testGetSalesReportWithGrouping(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/sales?start_date=2025-01-01&end_date=2025-12-31&group_by=product&limit=50');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'sales',
                'filters' => [
                    'group_by' => 'product'
                ]
            ]
        ]);
    }

    public function testGetIncomeReport(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/income?start_date=2025-01-01&end_date=2025-12-31');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'income',
                'period' => [
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-12-31'
                ]
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.incomes');
        $I->seeResponseJsonMatchesJsonPath('$.data.summary');
    }

    public function testGetIncomeReportWithFilters(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/income?start_date=2025-01-01&end_date=2025-12-31&product_id=' . $this->testProduct->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'income',
                'filters' => [
                    'product_id' => $this->testProduct->id
                ]
            ]
        ]);
    }

    public function testGetSummaryReport(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/summary?start_date=2025-01-01&end_date=2025-12-31');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'summary',
                'period' => [
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-12-31'
                ]
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.sales_summary');
        $I->seeResponseJsonMatchesJsonPath('$.data.income_summary');
        $I->seeResponseJsonMatchesJsonPath('$.data.stock_summary');
        $I->seeResponseJsonMatchesJsonPath('$.data.top_products');
    }

    public function testGetSummaryReportWithMetrics(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/summary?start_date=2025-01-01&end_date=2025-12-31&include_metrics=true');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'summary'
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.performance_metrics');
    }

    public function testReportWithInvalidDateRange(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/sales?start_date=2025-12-31&end_date=2025-01-01'); // Invalid range
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Start date must be before end date'
            ]
        ]);
    }

    public function testReportWithInvalidDateFormat(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/sales?start_date=invalid-date&end_date=2025-12-31');
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid date format'
            ]
        ]);
    }

    public function testStockReportWithNonExistentCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/stock?category_id=99999');
        
        $I->seeResponseCodeIs(200); // Should return empty results, not error
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'stock'
            ]
        ]);
    }

    public function testReportResponseStructure(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/stock');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseJsonMatchesJsonPath('$.success');
        $I->seeResponseJsonMatchesJsonPath('$.data.report_type');
        $I->seeResponseJsonMatchesJsonPath('$.data.generated_at');
        $I->seeResponseJsonMatchesJsonPath('$._meta.execution_time');
        $I->seeResponseJsonMatchesJsonPath('$._meta.performance_level');
    }

    public function testReportWithLargeLimit(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/stock?limit=10000'); // Very large limit
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'filters' => [
                    'limit' => 1000 // Should be capped at maximum
                ]
            ]
        ]);
    }

    public function testReportCaching(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        
        // First request
        $I->sendGET('/api/reports/summary?start_date=2025-01-01&end_date=2025-01-31');
        $I->seeResponseCodeIs(200);
        $firstExecutionTime = $I->grabDataFromResponseByJsonPath('$._meta.execution_time')[0];
        
        // Second request (should be faster due to caching)
        $I->sendGET('/api/reports/summary?start_date=2025-01-01&end_date=2025-01-31');
        $I->seeResponseCodeIs(200);
        $secondExecutionTime = $I->grabDataFromResponseByJsonPath('$._meta.execution_time')[0];
        
        // Second request should be faster (cached)
        // Note: This test might be flaky depending on cache implementation
    }

    public function testReportWithDefaultDateRange(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/reports/sales'); // No date range provided
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'report_type' => 'sales'
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.period.start_date');
        $I->seeResponseJsonMatchesJsonPath('$.data.period.end_date');
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testProduct && $this->testProduct->id) {
            $this->testProduct->delete();
        }
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
