<?php

$params = require __DIR__ . '/params.php';
$db = require __DIR__ . '/db.php';

$config = [
    'id' => 'basic',
    'basePath' => dirname(__DIR__),
    'bootstrap' => ['log'],
    'aliases' => [
        '@bower' => '@vendor/bower-asset',
        '@npm'   => '@vendor/npm-asset',
    ],
    'components' => [        'request' => [
            // !!! insert a secret key in the following (if it is empty) - this is required by cookie validation
            'cookieValidationKey' => 'AL_jq4VCN0VnX-iX9j7cDPsz-l1aM0OQ',
            'parsers' => [
                'application/json' => 'yii\web\JsonParser',
            ]
        ],
        'cache' => [
            'class' => 'yii\caching\FileCache',
        ],
        'user' => [
            'identityClass' => 'app\models\User',
            'enableAutoLogin' => true,
        ],        'errorHandler' => [
            'class' => 'app\components\ApiErrorHandler',
            'errorAction' => 'site/error',
            'exceptionView' => '@app/views/site/error.php',
        ],
        'mailer' => [
            'class' => \yii\symfonymailer\Mailer::class,
            'viewPath' => '@app/mail',
            // send all mails to a file by default.
            'useFileTransport' => true,
        ],
        'log' => [
            'traceLevel' => YII_DEBUG ? 3 : 0,
            'targets' => [
                [
                    'class' => 'yii\log\FileTarget',
                    'levels' => ['error', 'warning'],
                ],
            ],
        ],        'db' => $db,
        'urlManager' => [
            'enablePrettyUrl' => true,
            'showScriptName' => false,
            'rules' => [
                // API endpoints
                'GET api/products' => 'api/product/index',
                'POST api/products' => 'api/product/create',
                'GET api/products/<id:\d+>' => 'api/product/view',
                'PUT api/products/<id:\d+>' => 'api/product/update',
                'PATCH api/products/<id:\d+>' => 'api/product/update',
                'DELETE api/products/<id:\d+>' => 'api/product/delete',
                
                'GET api/categories' => 'api/category/index',
                'POST api/categories' => 'api/category/create',
                'GET api/categories/<id:\d+>' => 'api/category/view',
                'PUT api/categories/<id:\d+>' => 'api/category/update',
                'PATCH api/categories/<id:\d+>' => 'api/category/update',
                'DELETE api/categories/<id:\d+>' => 'api/category/delete',
                
                'GET api/sales' => 'api/sale/index',
                'POST api/sales' => 'api/sale/create',
                'GET api/sales/<id:\d+>' => 'api/sale/view',
                'DELETE api/sales/<id:\d+>' => 'api/sale/delete',
                
                'GET api/incomes' => 'api/income/index',
                'POST api/incomes' => 'api/income/create',
                'GET api/incomes/<id:\d+>' => 'api/income/view',
                'DELETE api/incomes/<id:\d+>' => 'api/income/delete',
                
                'GET api/reports/stock' => 'api/report/stock',                'GET api/reports/sales' => 'api/report/sales',
                'GET api/reports/income' => 'api/report/income',
                'GET api/reports/summary' => 'api/report/summary',
                
                // Authentication routes
                'POST api/auth/login' => 'api/auth/login',
                'POST api/auth/verify' => 'api/auth/verify',
            ],
        ],
    ],
    'params' => $params,
];

if (YII_ENV_DEV) {
    // configuration adjustments for 'dev' environment
    $config['bootstrap'][] = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];

    $config['bootstrap'][] = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
        // uncomment the following to add your IP if you are not connecting from localhost.
        //'allowedIPs' => ['127.0.0.1', '::1'],
    ];
}

return $config;
