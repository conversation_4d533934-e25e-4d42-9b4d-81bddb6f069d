<?php

// Check if we have Supabase configuration
$supabaseUrl = getenv('SUPABASE_DB_URL');
$supabaseHost = getenv('SUPABASE_DB_HOST');
$supabaseDb = getenv('SUPABASE_DB_NAME');
$supabaseUser = getenv('SUPABASE_DB_USER');
$supabasePass = getenv('SUPABASE_DB_PASS');
$supabasePort = getenv('SUPABASE_DB_PORT') ?: '5432';

if ($supabaseUrl) {
    // Parse Supabase URL format: postgresql://user:pass@host:port/dbname
    $parsed = parse_url($supabaseUrl);
    return [
        'class' => 'yii\db\Connection',
        'dsn' => sprintf(
            'pgsql:host=%s;port=%d;dbname=%s;sslmode=require',
            $parsed['host'],
            $parsed['port'] ?? 5432,
            ltrim($parsed['path'], '/')
        ),
        'username' => $parsed['user'],
        'password' => $parsed['pass'],
        'charset' => 'utf8',
        'enableSchemaCache' => true,
        'schemaCacheDuration' => 3600,
    ];
} elseif ($supabaseHost && $supabaseDb && $supabaseUser && $supabasePass) {
    // Individual Supabase parameters
    return [
        'class' => 'yii\db\Connection',
        'dsn' => sprintf(
            'pgsql:host=%s;port=%s;dbname=%s;sslmode=require',
            $supabaseHost,
            $supabasePort,
            $supabaseDb
        ),
        'username' => $supabaseUser,
        'password' => $supabasePass,
        'charset' => 'utf8',
        'enableSchemaCache' => true,
        'schemaCacheDuration' => 3600,
    ];
} else {
    // Fallback to local PostgreSQL
    return [
        'class' => 'yii\db\Connection',
        'dsn' => 'pgsql:host=localhost;dbname=sklad',
        'username' => 'postgres',
        'password' => 'postgres',
        'charset' => 'utf8',
    ];
}
