<?php

/**
 * Quick API Test Script
 * Tests all major API endpoints with real HTTP requests
 */

class ApiTestScript
{
    private $baseUrl;
    private $authToken;
    private $testResults = [];

    public function __construct($baseUrl = 'http://localhost:8080')
    {
        $this->baseUrl = rtrim($baseUrl, '/');
    }

    /**
     * Run all tests
     */
    public function runAllTests()
    {
        echo "=== Warehouse API Test Script ===\n\n";
        
        // Test basic connectivity
        if (!$this->testConnectivity()) {
            echo "❌ API server not accessible at {$this->baseUrl}\n";
            return false;
        }
        
        // Test authentication
        if (!$this->testAuthentication()) {
            echo "❌ Authentication failed\n";
            return false;
        }
        
        // Run endpoint tests
        $this->testProductEndpoints();
        $this->testCategoryEndpoints();
        $this->testQuickOperations();
        $this->testReports();
        
        // Show results
        $this->showResults();
        
        return true;
    }

    /**
     * Test basic connectivity
     */
    private function testConnectivity()
    {
        echo "Testing API connectivity...\n";
        
        $response = $this->makeRequest('GET', '/api');
        
        if ($response && isset($response['success']) && $response['success']) {
            echo "✅ API server is accessible\n";
            return true;
        }
        
        return false;
    }

    /**
     * Test authentication
     */
    private function testAuthentication()
    {
        echo "Testing authentication...\n";
        
        // Try to login
        $loginData = [
            'username' => 'admin',
            'password' => 'admin123'
        ];
        
        $response = $this->makeRequest('POST', '/api/auth/login', $loginData);
        
        if ($response && isset($response['success']) && $response['success']) {
            $this->authToken = $response['data']['token'];
            echo "✅ Authentication successful\n";
            $this->testResults['auth_login'] = 'PASS';
            
            // Test token verification
            $verifyResponse = $this->makeRequest('POST', '/api/auth/verify', [
                'token' => $this->authToken
            ]);
            
            if ($verifyResponse && $verifyResponse['data']['valid']) {
                echo "✅ Token verification successful\n";
                $this->testResults['auth_verify'] = 'PASS';
                return true;
            } else {
                echo "❌ Token verification failed\n";
                $this->testResults['auth_verify'] = 'FAIL';
                return false;
            }
        } else {
            echo "❌ Authentication failed\n";
            $this->testResults['auth_login'] = 'FAIL';
            return false;
        }
    }

    /**
     * Test product endpoints
     */
    private function testProductEndpoints()
    {
        echo "Testing product endpoints...\n";
        
        // Get products
        $response = $this->makeAuthenticatedRequest('GET', '/api/products');
        $this->testResults['products_list'] = $response ? 'PASS' : 'FAIL';
        echo ($response ? "✅" : "❌") . " GET /api/products\n";
        
        // Create product
        $productData = [
            'name' => 'Test API Product',
            'barcode' => '9999999999999',
            'unit_type' => 'piece',
            'price_per_unit' => 25.99,
            'current_stock' => 10
        ];
        
        $createResponse = $this->makeAuthenticatedRequest('POST', '/api/products', $productData);
        $this->testResults['products_create'] = $createResponse ? 'PASS' : 'FAIL';
        echo ($createResponse ? "✅" : "❌") . " POST /api/products\n";
        
        if ($createResponse && isset($createResponse['data']['id'])) {
            $productId = $createResponse['data']['id'];
            
            // Get single product
            $getResponse = $this->makeAuthenticatedRequest('GET', "/api/products/{$productId}");
            $this->testResults['products_get'] = $getResponse ? 'PASS' : 'FAIL';
            echo ($getResponse ? "✅" : "❌") . " GET /api/products/{$productId}\n";
            
            // Update product
            $updateData = ['name' => 'Updated Test Product'];
            $updateResponse = $this->makeAuthenticatedRequest('PUT', "/api/products/{$productId}", $updateData);
            $this->testResults['products_update'] = $updateResponse ? 'PASS' : 'FAIL';
            echo ($updateResponse ? "✅" : "❌") . " PUT /api/products/{$productId}\n";
            
            // Delete product
            $deleteResponse = $this->makeAuthenticatedRequest('DELETE', "/api/products/{$productId}");
            $this->testResults['products_delete'] = $deleteResponse ? 'PASS' : 'FAIL';
            echo ($deleteResponse ? "✅" : "❌") . " DELETE /api/products/{$productId}\n";
        }
    }

    /**
     * Test category endpoints
     */
    private function testCategoryEndpoints()
    {
        echo "Testing category endpoints...\n";
        
        // Get categories
        $response = $this->makeAuthenticatedRequest('GET', '/api/categories');
        $this->testResults['categories_list'] = $response ? 'PASS' : 'FAIL';
        echo ($response ? "✅" : "❌") . " GET /api/categories\n";
        
        // Create category
        $categoryData = [
            'name' => 'Test API Category',
            'description' => 'Category created by API test'
        ];
        
        $createResponse = $this->makeAuthenticatedRequest('POST', '/api/categories', $categoryData);
        $this->testResults['categories_create'] = $createResponse ? 'PASS' : 'FAIL';
        echo ($createResponse ? "✅" : "❌") . " POST /api/categories\n";
        
        if ($createResponse && isset($createResponse['data']['id'])) {
            $categoryId = $createResponse['data']['id'];
            
            // Delete category
            $deleteResponse = $this->makeAuthenticatedRequest('DELETE', "/api/categories/{$categoryId}");
            $this->testResults['categories_delete'] = $deleteResponse ? 'PASS' : 'FAIL';
            echo ($deleteResponse ? "✅" : "❌") . " DELETE /api/categories/{$categoryId}\n";
        }
    }

    /**
     * Test quick operations
     */
    private function testQuickOperations()
    {
        echo "Testing quick operations...\n";
        
        // Test quick income search
        $searchResponse = $this->makeAuthenticatedRequest('GET', '/api/income/search?q=test&limit=5');
        $this->testResults['income_search'] = $searchResponse ? 'PASS' : 'FAIL';
        echo ($searchResponse ? "✅" : "❌") . " GET /api/income/search\n";
        
        // Test quick sale search
        $saleSearchResponse = $this->makeAuthenticatedRequest('GET', '/api/sales/search?q=test&limit=5');
        $this->testResults['sales_search'] = $saleSearchResponse ? 'PASS' : 'FAIL';
        echo ($saleSearchResponse ? "✅" : "❌") . " GET /api/sales/search\n";
        
        // Test popular products
        $popularResponse = $this->makeAuthenticatedRequest('GET', '/api/sales/popular?limit=5');
        $this->testResults['sales_popular'] = $popularResponse ? 'PASS' : 'FAIL';
        echo ($popularResponse ? "✅" : "❌") . " GET /api/sales/popular\n";
    }

    /**
     * Test reports
     */
    private function testReports()
    {
        echo "Testing reports...\n";
        
        $reports = [
            'stock' => '/api/reports/stock',
            'sales' => '/api/reports/sales?start_date=2025-01-01&end_date=2025-12-31',
            'income' => '/api/reports/income?start_date=2025-01-01&end_date=2025-12-31',
            'summary' => '/api/reports/summary?start_date=2025-01-01&end_date=2025-12-31'
        ];
        
        foreach ($reports as $name => $endpoint) {
            $response = $this->makeAuthenticatedRequest('GET', $endpoint);
            $this->testResults["report_{$name}"] = $response ? 'PASS' : 'FAIL';
            echo ($response ? "✅" : "❌") . " GET {$endpoint}\n";
        }
    }

    /**
     * Make authenticated request
     */
    private function makeAuthenticatedRequest($method, $endpoint, $data = null)
    {
        $headers = [];
        if ($this->authToken) {
            $headers[] = "Authorization: Bearer {$this->authToken}";
        }
        
        return $this->makeRequest($method, $endpoint, $data, $headers);
    }

    /**
     * Make HTTP request
     */
    private function makeRequest($method, $endpoint, $data = null, $headers = [])
    {
        $url = $this->baseUrl . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        
        // Add default headers
        $defaultHeaders = [
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $allHeaders = array_merge($defaultHeaders, $headers);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $allHeaders);
        
        // Add data for POST/PUT requests
        if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            echo "CURL Error: {$error}\n";
            return false;
        }
        
        if ($httpCode >= 400) {
            echo "HTTP Error {$httpCode} for {$method} {$endpoint}\n";
            return false;
        }
        
        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            echo "JSON decode error for {$method} {$endpoint}\n";
            return false;
        }
        
        return $decoded;
    }

    /**
     * Show test results
     */
    private function showResults()
    {
        echo "\n=== Test Results ===\n";
        
        $passed = 0;
        $total = count($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = $result === 'PASS' ? '✅' : '❌';
            echo "{$status} {$test}: {$result}\n";
            
            if ($result === 'PASS') {
                $passed++;
            }
        }
        
        echo "\nSummary: {$passed}/{$total} tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All tests passed!\n";
        } else {
            echo "❌ Some tests failed. Check the API server and database.\n";
        }
    }
}

// Run tests if script is executed directly
if (php_sapi_name() === 'cli') {
    $baseUrl = $argv[1] ?? 'http://localhost:8080';
    
    echo "Testing API at: {$baseUrl}\n\n";
    
    $tester = new ApiTestScript($baseUrl);
    $result = $tester->runAllTests();
    
    exit($result ? 0 : 1);
}
