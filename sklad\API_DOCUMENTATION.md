# Warehouse API Documentation

This document describes the RESTful API endpoints available in the Warehouse Management System.

## Authentication

### Login

**POST /api/auth/login**

Authenticates a user and returns a JWT token.

**Request Body:**
```json
{
  "username": "admin",
  "password": "admin"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "100",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    }
  },
  "error": null
}
```

### Verify Token

**POST /api/auth/verify**

Verifies if a JWT token is valid.

**Request Body:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "user": {
      "id": "100",
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    },
    "expires": "2025-06-10 12:34:56"
  },
  "error": null
}
```

## Products

### List Products

**GET /api/products**

Returns a list of all products with support for filtering, sorting, and pagination.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 20, max: 100)
- `search` - Search term to find in name, SKU or description
- `category` - Filter by category ID
- `min_stock` - Filter by minimum stock level
- `max_stock` - Filter by maximum stock level

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Product Name",
      "sku": "PRD-001",
      "description": "Product description",
      "category_id": 1,
      "unit": "шт",
      "price": 100.50,
      "current_stock": 50,
      "created_at": "2025-06-01 12:34:56",
      "updated_at": "2025-06-01 12:34:56"
    }
  ],
  "error": null,
  "_meta": {
    "totalCount": 42,
    "pageCount": 3,
    "currentPage": 1,
    "perPage": 20
  }
}
```

### Get Product

**GET /api/products/{id}**

Returns details for a specific product.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Product Name",
    "sku": "PRD-001",
    "description": "Product description",
    "category_id": 1,
    "unit": "шт",
    "price": 100.50,
    "current_stock": 50,
    "created_at": "2025-06-01 12:34:56",
    "updated_at": "2025-06-01 12:34:56"
  },
  "error": null
}
```

### Create Product

**POST /api/products**

Creates a new product.

**Request Body:**
```json
{
  "name": "New Product",
  "sku": "PRD-002",
  "description": "New product description",
  "category_id": 1,
  "unit": "шт",
  "price": 200.75,
  "current_stock": 100
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "New Product",
    "sku": "PRD-002",
    "description": "New product description",
    "category_id": 1,
    "unit": "шт",
    "price": 200.75,
    "current_stock": 100,
    "created_at": "2025-06-03 15:40:22",
    "updated_at": "2025-06-03 15:40:22"
  },
  "error": null
}
```

### Update Product

**PUT /api/products/{id}**

Updates an existing product.

**Request Body:**
```json
{
  "name": "Updated Product Name",
  "price": 250.00,
  "current_stock": 120
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Updated Product Name",
    "sku": "PRD-002",
    "description": "New product description",
    "category_id": 1,
    "unit": "шт",
    "price": 250.00,
    "current_stock": 120,
    "created_at": "2025-06-03 15:40:22",
    "updated_at": "2025-06-03 15:45:10"
  },
  "error": null
}
```

### Delete Product

**DELETE /api/products/{id}**

Deletes a product.

**Response:**
```json
{
  "success": true,
  "data": null,
  "error": null
}
```

## Categories

### List Categories

**GET /api/categories**

Returns a list of all categories with support for filtering, sorting, and pagination.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 20, max: 100)
- `search` - Search term to find in name or description
- `with_products_count` - Include count of products in each category (true/false)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Category Name",
      "description": "Category description",
      "created_at": "2025-06-01 12:34:56",
      "updated_at": "2025-06-01 12:34:56"
    }
  ],
  "error": null,
  "_meta": {
    "totalCount": 15,
    "pageCount": 1,
    "currentPage": 1,
    "perPage": 20
  }
}
```

### Get Category

**GET /api/categories/{id}**

Returns details for a specific category.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "Category Name",
    "description": "Category description",
    "created_at": "2025-06-01 12:34:56",
    "updated_at": "2025-06-01 12:34:56"
  },
  "error": null
}
```

### Create Category

**POST /api/categories**

Creates a new category.

**Request Body:**
```json
{
  "name": "New Category",
  "description": "New category description"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "New Category",
    "description": "New category description",
    "created_at": "2025-06-03 15:50:22",
    "updated_at": "2025-06-03 15:50:22"
  },
  "error": null
}
```

### Update Category

**PUT /api/categories/{id}**

Updates an existing category.

**Request Body:**
```json
{
  "name": "Updated Category Name",
  "description": "Updated description"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "name": "Updated Category Name",
    "description": "Updated description",
    "created_at": "2025-06-03 15:50:22",
    "updated_at": "2025-06-03 15:55:10"
  },
  "error": null
}
```

### Delete Category

**DELETE /api/categories/{id}**

Deletes a category. Will fail if the category contains products.

**Response:**
```json
{
  "success": true,
  "data": null,
  "error": null
}
```

## Sales

### List Sales

**GET /api/sales**

Returns a list of all sales with support for filtering, sorting, and pagination.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 20, max: 100)
- `start_date` - Filter by start date (YYYY-MM-DD)
- `end_date` - Filter by end date (YYYY-MM-DD)
- `product_id` - Filter by product ID
- `min_amount` - Filter by minimum total amount
- `max_amount` - Filter by maximum total amount

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "product_id": 1,
      "quantity": 5,
      "price_per_unit": 100.50,
      "total_amount": 502.50,
      "notes": "Sale notes",
      "created_at": "2025-06-01 12:34:56"
    }
  ],
  "error": null,
  "_meta": {
    "totalCount": 35,
    "pageCount": 2,
    "currentPage": 1,
    "perPage": 20
  }
}
```

### Get Sale

**GET /api/sales/{id}**

Returns details for a specific sale.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "product_id": 1,
    "quantity": 5,
    "price_per_unit": 100.50,
    "total_amount": 502.50,
    "notes": "Sale notes",
    "created_at": "2025-06-01 12:34:56"
  },
  "error": null
}
```

### Create Sale

**POST /api/sales**

Creates a new sale.

**Request Body:**
```json
{
  "product_id": 1,
  "quantity": 3,
  "price_per_unit": 100.50,
  "notes": "Sale notes"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "product_id": 1,
    "quantity": 3,
    "price_per_unit": 100.50,
    "total_amount": 301.50,
    "notes": "Sale notes",
    "created_at": "2025-06-03 16:00:22",
    "product": {
      "id": 1,
      "name": "Product Name",
      "sku": "PRD-001"
    }
  },
  "error": null
}
```

### Delete Sale

**DELETE /api/sales/{id}**

Deletes a sale.

**Response:**
```json
{
  "success": true,
  "data": null,
  "error": null
}
```

## Income (Purchases)

### List Incomes

**GET /api/incomes**

Returns a list of all income/purchase records with support for filtering, sorting, and pagination.

**Query Parameters:**
- `page` - Page number (default: 1)
- `limit` - Results per page (default: 20, max: 100)
- `start_date` - Filter by start date (YYYY-MM-DD)
- `end_date` - Filter by end date (YYYY-MM-DD)
- `product_id` - Filter by product ID
- `min_cost` - Filter by minimum total cost
- `max_cost` - Filter by maximum total cost

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "product_id": 1,
      "quantity": 10,
      "price_per_unit": 80.00,
      "total_cost": 800.00,
      "notes": "Purchase notes",
      "created_at": "2025-06-01 12:34:56"
    }
  ],
  "error": null,
  "_meta": {
    "totalCount": 28,
    "pageCount": 2,
    "currentPage": 1,
    "perPage": 20
  }
}
```

### Get Income

**GET /api/incomes/{id}**

Returns details for a specific income/purchase.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 1,
    "product_id": 1,
    "quantity": 10,
    "price_per_unit": 80.00,
    "total_cost": 800.00,
    "notes": "Purchase notes",
    "created_at": "2025-06-01 12:34:56"
  },
  "error": null
}
```

### Create Income

**POST /api/incomes**

Creates a new income/purchase record.

**Request Body:**
```json
{
  "product_id": 1,
  "quantity": 5,
  "price_per_unit": 80.00,
  "notes": "Purchase notes"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": 2,
    "product_id": 1,
    "quantity": 5,
    "price_per_unit": 80.00,
    "total_cost": 400.00,
    "notes": "Purchase notes",
    "created_at": "2025-06-03 16:10:22",
    "product": {
      "id": 1,
      "name": "Product Name",
      "sku": "PRD-001"
    }
  },
  "error": null
}
```

### Delete Income

**DELETE /api/incomes/{id}**

Deletes an income/purchase record.

**Response:**
```json
{
  "success": true,
  "data": null,
  "error": null
}
```

## Reports

### Stock Report

**GET /api/reports/stock**

Returns the current stock report.

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "Product Name",
      "sku": "PRD-001",
      "current_stock": 55,
      "unit": "шт",
      "price": 100.50,
      "category_name": "Category Name"
    }
  ],
  "error": null
}
```

### Sales Report

**GET /api/reports/sales?start=2025-06-01&end=2025-06-30**

Returns the sales report for a specified date range.

**Query Parameters:**
- `start` - Start date (YYYY-MM-DD)
- `end` - End date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "quantity": 5,
      "price_per_unit": 100.50,
      "total_amount": 502.50,
      "notes": "Sale notes",
      "created_at": "2025-06-01 12:34:56",
      "product_name": "Product Name",
      "product_sku": "PRD-001"
    }
  ],
  "error": null
}
```

### Income Report

**GET /api/reports/income?start=2025-06-01&end=2025-06-30**

Returns the income/purchase report for a specified date range.

**Query Parameters:**
- `start` - Start date (YYYY-MM-DD)
- `end` - End date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "quantity": 10,
      "price_per_unit": 80.00,
      "total_cost": 800.00,
      "notes": "Purchase notes",
      "created_at": "2025-06-01 12:34:56",
      "product_name": "Product Name",
      "product_sku": "PRD-001"
    }
  ],
  "error": null
}
```

### Summary Report

**GET /api/reports/summary?start=2025-06-01&end=2025-06-30**

Returns a summary report with statistics for the specified date range.

**Query Parameters:**
- `start` - Start date (YYYY-MM-DD)
- `end` - End date (YYYY-MM-DD)

**Response:**
```json
{
  "success": true,
  "data": {
    "period": {
      "start": "2025-06-01",
      "end": "2025-06-30"
    },
    "sales": {
      "total": 804.00,
      "count": 2
    },
    "purchases": {
      "total": 1200.00,
      "count": 2
    },
    "profit": -396.00,
    "inventory": {
      "value": 5527.50,
      "products_count": 3
    }
  },
  "error": null
}
```

## Error Responses

All endpoints return a standardized error format:

```json
{
  "success": false,
  "data": null,
  "error": {
    "message": "Error message",
    "details": {
      "field": [
        "Error description"
      ]
    }
  }
}
```

Common HTTP status codes:
- 200: Success
- 201: Created
- 204: No Content (successful delete)
- 400: Bad Request
- 401: Unauthorized
- 404: Not Found
- 500: Server Error
