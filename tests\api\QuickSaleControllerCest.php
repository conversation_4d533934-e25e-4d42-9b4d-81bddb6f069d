<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\components\JwtHelper;

class QuickSaleControllerCest
{
    private $authToken;
    private $testCategory;
    private $testProduct;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->save();

        // Create test product with stock
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $this->testCategory->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 20; // Sufficient stock for tests
        $this->testProduct->save();
    }

    public function testScanBarcodeSuccess(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 2,
            'price_per_unit' => 35.99,
            'notes' => 'Test sale'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 2,
                    'price_per_unit' => 35.99,
                    'notes' => 'Test sale'
                ],
                'product' => [
                    'barcode' => '1234567890123',
                    'previous_stock' => 20,
                    'current_stock' => 18
                ]
            ]
        ]);
    }

    public function testScanBarcodeWithoutAuth(ApiTester $I)
    {
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 2,
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(401);
    }

    public function testScanBarcodeInvalidBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => 'invalid',
            'quantity' => 2,
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid barcode format'
            ]
        ]);
    }

    public function testScanBarcodeProductNotFound(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '9999999999999',
            'quantity' => 2,
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Product not found'
            ]
        ]);
    }

    public function testScanBarcodeInsufficientStock(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 100, // More than available stock
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Insufficient stock'
            ]
        ]);
    }

    public function testScanBarcodeWithProductPrice(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 1,
            // No price_per_unit - should use product price
            'notes' => 'Using product price'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 1,
                    'price_per_unit' => 25.99 // Product's default price
                ]
            ]
        ]);
    }

    public function testQuickEntrySuccess(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/quick-entry', [
            'product' => [
                'id' => $this->testProduct->id
            ],
            'quantity' => 3,
            'price_per_unit' => 30.00,
            'notes' => 'Quick sale test'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 3,
                    'price_per_unit' => 30.00
                ]
            ]
        ]);
    }

    public function testQuickEntryByName(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/quick-entry', [
            'product' => [
                'name' => 'Test Product'
            ],
            'quantity' => 1,
            'price_per_unit' => 25.99
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 1,
                    'price_per_unit' => 25.99
                ]
            ]
        ]);
    }

    public function testQuickEntryByBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/quick-entry', [
            'product' => [
                'barcode' => '1234567890123'
            ],
            'quantity' => 2,
            'price_per_unit' => 28.00
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'sale' => [
                    'quantity' => 2,
                    'price_per_unit' => 28.00
                ]
            ]
        ]);
    }

    public function testQuickEntryProductNotFound(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/quick-entry', [
            'product' => [
                'name' => 'Non Existent Product'
            ],
            'quantity' => 1,
            'price_per_unit' => 20.00
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Product not found'
            ]
        ]);
    }

    public function testSearchProductsForSale(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/sales/search?q=Test&limit=10');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'query' => 'Test'
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.products');
    }

    public function testSearchProductsEmptyQuery(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/sales/search?q=');
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Search query is required'
            ]
        ]);
    }

    public function testGetPopularProducts(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/sales/popular?limit=5&days=30');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'period_days' => 30
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.products');
    }

    public function testInvalidQuantity(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => -1, // Negative quantity
            'price_per_unit' => 35.99
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Quantity must be greater than 0'
            ]
        ]);
    }

    public function testZeroPrice(ApiTester $I)
    {
        // Create product with zero price
        $zeroProduct = new Product();
        $zeroProduct->name = 'Zero Price Product';
        $zeroProduct->barcode = '8888888888888';
        $zeroProduct->category_id = $this->testCategory->id;
        $zeroProduct->unit_type = Product::UNIT_PIECE;
        $zeroProduct->price_per_unit = 0; // Zero price
        $zeroProduct->current_stock = 10;
        $zeroProduct->save();

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/sales/scan-barcode', [
            'barcode' => '8888888888888',
            'quantity' => 1
            // No price_per_unit provided and product has zero price
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Price per unit must be greater than 0'
            ]
        ]);

        $zeroProduct->delete();
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testProduct && $this->testProduct->id) {
            $this->testProduct->delete();
        }
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
