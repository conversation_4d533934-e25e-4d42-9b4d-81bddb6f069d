a:14:{s:6:"config";s:1021:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:13:"Warehouse API";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:3:{s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.26.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:56:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-gii/src";}}}}";s:3:"log";s:15157:"a:1:{s:8:"messages";a:19:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.805934;i:4;a:0:{}i:5;i:2743744;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.80596;i:4;a:0:{}i:5;i:2744920;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.817966;i:4;a:0:{}i:5;i:3640328;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.821306;i:4;a:0:{}i:5;i:4300152;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.8216;i:4;a:0:{}i:5;i:4324984;}i:23;a:6:{i:0;s:38:"Route requested: 'api/category/delete'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.823675;i:4;a:0:{}i:5;i:4562648;}i:24;a:6:{i:0;s:33:"Route to run: api/category/delete";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.82665;i:4;a:0:{}i:5;i:4820120;}i:25;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.839778;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6296416;}i:28;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.013555;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6501360;}i:31;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.048799;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6549800;}i:34;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=3) AND ("status"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.056919;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6848824;}i:37;a:6:{i:0;s:55:"User '3' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1748972611.066927;i:4;a:1:{i:0;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:7260088;}i:38;a:6:{i:0;s:70:"Running action: app\controllers\api\CategoryController::actionDelete()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1748972611.066988;i:4;a:0:{}i:5;i:7259016;}i:39;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'category'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.067244;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7294816;}i:42;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='category'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.073896;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7303712;}i:45;a:6:{i:0;s:39:"SELECT * FROM "category" WHERE "id"='1'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.079373;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7308096;}i:48;a:6:{i:0;s:52:"SELECT COUNT(*) FROM "product" WHERE "category_id"=1";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.086874;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"D:\OSPanel\domains\warehouse\models\Category.php";s:4:"line";i:91;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:114;s:8:"function";s:16:"getProductsCount";s:5:"class";s:19:"app\models\Category";s:4:"type";s:2:"->";}}i:5;i:7394720;}i:51;a:6:{i:0;s:35:"DELETE FROM "category" WHERE "id"=1";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748972611.100651;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:118;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7399592;}i:54;a:6:{i:0;s:65:"Slow API request: api/categories/1 took 277.01ms (Memory: 0.00MB)";i:1;i:2;i:2;s:11:"performance";i:3;d:1748972611.103142;i:4;a:3:{i:0;a:5:{s:4:"file";s:62:"D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php";s:4:"line";i:108;s:8:"function";s:7:"warning";s:5:"class";s:11:"yii\BaseYii";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:62:"D:\OSPanel\domains\warehouse\components\PerformanceMonitor.php";s:4:"line";i:68;s:8:"function";s:14:"logSlowRequest";s:5:"class";s:33:"app\components\PerformanceMonitor";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:63:"D:\OSPanel\domains\warehouse\controllers\api\BaseController.php";s:4:"line";i:126;s:8:"function";s:10:"endRequest";s:5:"class";s:33:"app\components\PerformanceMonitor";s:4:"type";s:2:"->";}}i:5;i:7403480;}}}";s:9:"profiling";s:25537:"a:3:{s:6:"memory";i:7595560;s:4:"time";d:0.31785011291503906;s:8:"messages";a:18:{i:26;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.839794;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6298296;}i:27;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1748972611.010551;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6301040;}i:29;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.013652;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6503688;}i:30;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.045367;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6523224;}i:32;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.048891;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6552040;}i:33;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.053427;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6555632;}i:35;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=3) AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.056955;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6852224;}i:36;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=3) AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.063161;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6855920;}i:40;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'category'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.067278;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7296680;}i:41;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'category'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.073406;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7307760;}i:43;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='category'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.073925;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7305576;}i:44;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='category'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.077779;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7307512;}i:46;a:6:{i:0;s:39:"SELECT * FROM "category" WHERE "id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.079428;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7311080;}i:47;a:6:{i:0;s:39:"SELECT * FROM "category" WHERE "id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.08361;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7313536;}i:49;a:6:{i:0;s:52:"SELECT COUNT(*) FROM "product" WHERE "category_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.086929;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"D:\OSPanel\domains\warehouse\models\Category.php";s:4:"line";i:91;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:114;s:8:"function";s:16:"getProductsCount";s:5:"class";s:19:"app\models\Category";s:4:"type";s:2:"->";}}i:5;i:7397720;}i:50;a:6:{i:0;s:52:"SELECT COUNT(*) FROM "product" WHERE "category_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.100091;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"D:\OSPanel\domains\warehouse\models\Category.php";s:4:"line";i:91;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:114;s:8:"function";s:16:"getProductsCount";s:5:"class";s:19:"app\models\Category";s:4:"type";s:2:"->";}}i:5;i:7399328;}i:52;a:6:{i:0;s:35:"DELETE FROM "category" WHERE "id"=1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748972611.100677;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:118;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7402200;}i:53;a:6:{i:0;s:35:"DELETE FROM "category" WHERE "id"=1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748972611.102829;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:118;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7403384;}}}";s:2:"db";s:24009:"a:1:{s:8:"messages";a:16:{i:29;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.013652;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6503688;}i:30;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.045367;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6523224;}i:32;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.048891;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6552040;}i:33;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.053427;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6555632;}i:35;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=3) AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.056955;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6852224;}i:36;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=3) AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.063161;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6855920;}i:40;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'category'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.067278;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7296680;}i:41;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'category'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.073406;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7307760;}i:43;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='category'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.073925;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7305576;}i:44;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='category'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.077779;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7307512;}i:46;a:6:{i:0;s:39:"SELECT * FROM "category" WHERE "id"='1'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.079428;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7311080;}i:47;a:6:{i:0;s:39:"SELECT * FROM "category" WHERE "id"='1'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.08361;i:4;a:2:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:112;s:8:"function";s:9:"findModel";s:5:"class";s:38:"app\controllers\api\CategoryController";s:4:"type";s:2:"->";}}i:5;i:7313536;}i:49;a:6:{i:0;s:52:"SELECT COUNT(*) FROM "product" WHERE "category_id"=1";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.086929;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"D:\OSPanel\domains\warehouse\models\Category.php";s:4:"line";i:91;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:114;s:8:"function";s:16:"getProductsCount";s:5:"class";s:19:"app\models\Category";s:4:"type";s:2:"->";}}i:5;i:7397720;}i:50;a:6:{i:0;s:52:"SELECT COUNT(*) FROM "product" WHERE "category_id"=1";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748972611.100091;i:4;a:2:{i:0;a:5:{s:4:"file";s:48:"D:\OSPanel\domains\warehouse\models\Category.php";s:4:"line";i:91;s:8:"function";s:5:"count";s:5:"class";s:12:"yii\db\Query";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:114;s:8:"function";s:16:"getProductsCount";s:5:"class";s:19:"app\models\Category";s:4:"type";s:2:"->";}}i:5;i:7399328;}i:52;a:6:{i:0;s:35:"DELETE FROM "category" WHERE "id"=1";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748972611.100677;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:118;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7402200;}i:53;a:6:{i:0;s:35:"DELETE FROM "category" WHERE "id"=1";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748972611.102829;i:4;a:1:{i:0;a:5:{s:4:"file";s:67:"D:\OSPanel\domains\warehouse\controllers\api\CategoryController.php";s:4:"line";i:118;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7403384;}}}";s:5:"event";s:3660:"a:21:{i:0;a:5:{s:4:"time";d:**********.823304;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.826843;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.837147;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:3;a:5:{s:4:"time";d:1748972611.010507;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:4;a:5:{s:4:"time";d:1748972611.063926;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:5;a:5:{s:4:"time";d:1748972611.063973;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:6;a:5:{s:4:"time";d:1748972611.064072;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:7;a:5:{s:4:"time";d:1748972611.066952;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:1748972611.066969;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"app\controllers\api\CategoryController";}i:9;a:5:{s:4:"time";d:1748972611.067182;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:1748972611.08404;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\models\Category";}i:11;a:5:{s:4:"time";d:1748972611.08408;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\models\Category";}i:12;a:5:{s:4:"time";d:1748972611.084595;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:13;a:5:{s:4:"time";d:1748972611.100551;s:4:"name";s:12:"beforeDelete";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\models\Category";}i:14;a:5:{s:4:"time";d:1748972611.103073;s:4:"name";s:11:"afterDelete";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"app\models\Category";}i:15;a:5:{s:4:"time";d:1748972611.103186;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:38:"app\controllers\api\CategoryController";}i:16;a:5:{s:4:"time";d:1748972611.103679;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:17;a:5:{s:4:"time";d:1748972611.103689;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:18;a:5:{s:4:"time";d:1748972611.103696;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:19;a:5:{s:4:"time";d:1748972611.104204;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:20;a:5:{s:4:"time";d:1748972611.104265;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.789352;s:3:"end";d:1748972611.107322;s:6:"memory";i:7595560;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:3604:"a:3:{s:8:"messages";a:18:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823591;i:4;a:0:{}i:5;i:4548496;}i:6;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823602;i:4;a:0:{}i:5;i:4549248;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823607;i:4;a:0:{}i:5;i:4550000;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.82361;i:4;a:0:{}i:5;i:4550752;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823614;i:4;a:0:{}i:5;i:4551824;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:16:"GET api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823623;i:4;a:0:{}i:5;i:4552624;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:17:"POST api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823627;i:4;a:0:{}i:5;i:4553424;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.82363;i:4;a:0:{}i:5;i:4554232;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823633;i:4;a:0:{}i:5;i:4555040;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:27:"PATCH api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823636;i:4;a:0:{}i:5;i:4555848;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:28:"DELETE api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823641;i:4;a:0:{}i:5;i:4556656;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:18:"GET api/categories";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823644;i:4;a:0:{}i:5;i:4557456;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:19:"POST api/categories";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823648;i:4;a:0:{}i:5;i:4558896;}i:18;a:6:{i:0;a:3:{s:4:"rule";s:27:"GET api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.82365;i:4;a:0:{}i:5;i:4559704;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:27:"PUT api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823653;i:4;a:0:{}i:5;i:4560512;}i:20;a:6:{i:0;a:3:{s:4:"rule";s:29:"PATCH api/categories/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823656;i:4;a:0:{}i:5;i:4561320;}i:21;a:6:{i:0;s:53:"Request parsed with URL rule: api/categories/<id:\d+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.823665;i:4;a:0:{}i:5;i:4562984;}i:22;a:6:{i:0;a:3:{s:4:"rule";s:30:"DELETE api/categories/<id:\d+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.823669;i:4;a:0:{}i:5;i:4563336;}}s:5:"route";s:19:"api/category/delete";s:6:"action";s:54:"app\controllers\api\CategoryController::actionDelete()";}";s:7:"request";s:2396:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:204;s:14:"requestHeaders";a:4:{s:4:"host";s:14:"localhost:8080";s:12:"content-type";s:16:"application/json";s:6:"accept";s:16:"application/json";s:13:"authorization";s:195:"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDg5NzI2MDYsImV4cCI6MTc0OTU3NzQwNiwidWlkIjozLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZSI6ImFkbWluIn0.RN1VhZV31yagyYpZ8RepXuc2-OGurwpAMG3IWMPUHuk";}s:15:"responseHeaders";a:6:{s:12:"X-Powered-By";s:10:"PHP/8.1.31";s:32:"Access-Control-Allow-Credentials";s:4:"true";s:11:"X-Debug-Tag";s:13:"683f3442c7d25";s:16:"X-Debug-Duration";s:3:"316";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683f3442c7d25";s:10:"Set-Cookie";s:204:"_csrf=66174af1296460ed66ea6d267a052875b87627ade1e09acd7fc92f481678ae58a%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22JrANdCdOEjzVCZ2m4I7yp_Y62LStqyN4%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:19:"api/category/delete";s:6:"action";s:54:"app\controllers\api\CategoryController::actionDelete()";s:12:"actionParams";a:1:{s:2:"id";s:1:"1";}s:7:"general";a:5:{s:6:"method";s:6:"DELETE";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:20:{s:13:"DOCUMENT_ROOT";s:32:"D:\OSPanel\domains\warehouse\web";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:11:"REMOTE_PORT";s:5:"60813";s:15:"SERVER_SOFTWARE";s:29:"PHP 8.1.31 Development Server";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_PORT";s:4:"8080";s:11:"REQUEST_URI";s:17:"/api/categories/1";s:14:"REQUEST_METHOD";s:6:"DELETE";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"D:\OSPanel\domains\warehouse\web\index.php";s:9:"PATH_INFO";s:17:"/api/categories/1";s:8:"PHP_SELF";s:27:"/index.php/api/categories/1";s:9:"HTTP_HOST";s:14:"localhost:8080";s:12:"CONTENT_TYPE";s:16:"application/json";s:17:"HTTP_CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ACCEPT";s:16:"application/json";s:18:"HTTP_AUTHORIZATION";s:195:"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDg5NzI2MDYsImV4cCI6MTc0OTU3NzQwNiwidWlkIjozLCJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZSI6ImFkbWluIn0.RN1VhZV31yagyYpZ8RepXuc2-OGurwpAMG3IWMPUHuk";s:18:"REQUEST_TIME_FLOAT";d:**********.787024;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:2:"id";s:1:"1";}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:1212:"a:5:{s:2:"id";i:3;s:8:"identity";a:10:{s:2:"id";s:1:"3";s:8:"username";s:10:"'testuser'";s:5:"email";s:18:"'<EMAIL>'";s:13:"password_hash";s:62:"'$2y$13$yGmRH8CjDkQUeml1OHbjz.vtXh/KDviWXP2.fCnu8zQgsDgN0U9je'";s:8:"auth_key";s:34:"'1Ccr_14NHXizKz2cdjBWMFLGn31UVrGu'";s:4:"role";s:7:"'admin'";s:6:"status";s:1:"1";s:10:"created_at";s:21:"'2025-06-03 22:43:26'";s:10:"updated_at";s:21:"'2025-06-03 22:43:26'";s:13:"last_login_at";s:4:"null";}s:10:"attributes";a:10:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:5;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:13:"last_login_at";s:5:"label";s:13:"Last Login At";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683f3442c7d25";s:3:"url";s:38:"http://localhost:8080/api/categories/1";s:4:"ajax";i:0;s:6:"method";s:6:"DELETE";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.787024;s:10:"statusCode";i:204;s:8:"sqlCount";i:8;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7595560;s:14:"processingTime";d:0.31785011291503906;}s:10:"exceptions";a:0:{}}