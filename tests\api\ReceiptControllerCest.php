<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\models\Sale;
use app\models\Receipt;
use app\components\JwtHelper;

class ReceiptControllerCest
{
    private $authToken;
    private $testCategory;
    private $testProduct;
    private $testSale;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->save();

        // Create test product
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $this->testCategory->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 20;
        $this->testProduct->save();

        // Create test sale
        $this->testSale = new Sale();
        $this->testSale->product_id = $this->testProduct->id;
        $this->testSale->quantity = 2;
        $this->testSale->price_per_unit = 30.00;
        $this->testSale->notes = 'Test sale for receipt';
        $this->testSale->save();
    }

    public function testGenerateReceiptFromSale(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/generate', [
            'sale_id' => $this->testSale->id,
            'options' => [
                'payment_method' => 'cash',
                'customer_info' => [
                    'name' => 'Test Customer'
                ],
                'notes' => 'Test receipt generation'
            ]
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'total_amount' => 60.00 // 2 * 30.00
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.receipt_number');
    }

    public function testGenerateReceiptFromMultipleSales(ApiTester $I)
    {
        // Create another sale
        $sale2 = new Sale();
        $sale2->product_id = $this->testProduct->id;
        $sale2->quantity = 1;
        $sale2->price_per_unit = 25.99;
        $sale2->notes = 'Second test sale';
        $sale2->save();

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/generate', [
            'sale_ids' => [$this->testSale->id, $sale2->id],
            'options' => [
                'payment_method' => 'card',
                'notes' => 'Batch receipt'
            ]
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'total_amount' => 85.99 // 60.00 + 25.99
            ]
        ]);

        $sale2->delete();
    }

    public function testGenerateReceiptWithoutAuth(ApiTester $I)
    {
        $I->sendPOST('/api/receipts/generate', [
            'sale_id' => $this->testSale->id
        ]);
        
        $I->seeResponseCodeIs(401);
    }

    public function testGenerateReceiptInvalidSale(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/generate', [
            'sale_id' => 99999 // Non-existent sale
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Sale not found'
            ]
        ]);
    }

    public function testGenerateReceiptMissingData(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/generate', [
            // Missing both sale_id and sale_ids
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Either sale_id or sale_ids must be provided'
            ]
        ]);
    }

    public function testViewReceipt(ApiTester $I)
    {
        // First generate a receipt
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/receipts/' . $receipt->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'receipt' => [
                    'id' => $receipt->id,
                    'receipt_number' => $receipt->receipt_number,
                    'type' => 'sale',
                    'payment_method' => 'cash'
                ]
            ]
        ]);

        $receipt->delete();
    }

    public function testViewNonExistentReceipt(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/receipts/99999');
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Receipt not found'
            ]
        ]);
    }

    public function testPrintReceiptAsHtml(ApiTester $I)
    {
        // Generate a receipt first
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/' . $receipt->id . '/print', [
            'format' => 'html',
            'template_type' => 'thermal'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'receipt_number' => $receipt->receipt_number
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.html_content');
        $I->seeResponseJsonMatchesJsonPath('$.data.printed_at');

        $receipt->delete();
    }

    public function testPrintReceiptAsPdf(ApiTester $I)
    {
        // Generate a receipt first
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/' . $receipt->id . '/print', [
            'format' => 'pdf',
            'template_type' => 'a4',
            'download' => false
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'receipt_number' => $receipt->receipt_number
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.pdf_content');

        $receipt->delete();
    }

    public function testPrintReceiptThermal(ApiTester $I)
    {
        // Generate a receipt first
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/' . $receipt->id . '/print', [
            'format' => 'thermal'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'receipt_number' => $receipt->receipt_number,
                'message' => 'Receipt marked as printed'
            ]
        ]);

        $receipt->delete();
    }

    public function testGetReceiptHistory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/receipts/history?start_date=2025-01-01&end_date=2025-12-31&limit=10');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'filters' => [
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-12-31'
                ]
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.receipts');
    }

    public function testGetReceiptStatistics(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/receipts/statistics?start_date=2025-01-01&end_date=2025-12-31');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'period' => [
                    'start_date' => '2025-01-01',
                    'end_date' => '2025-12-31'
                ]
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.statistics');
    }

    public function testDuplicateReceipt(ApiTester $I)
    {
        // Generate a receipt first
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/receipts/' . $receipt->id . '/duplicate', [
            'options' => [
                'payment_method' => 'card',
                'notes' => 'Duplicated receipt'
            ]
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'original_receipt_number' => $receipt->receipt_number
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.new_receipt_number');

        $receipt->delete();
    }

    public function testGetReceiptByNumber(ApiTester $I)
    {
        // Generate a receipt first
        $receipt = Receipt::createFromSale($this->testSale, [
            'payment_method' => 'cash'
        ]);

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/receipts/by-number/' . $receipt->receipt_number);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'receipt' => [
                    'receipt_number' => $receipt->receipt_number,
                    'type' => 'sale'
                ]
            ]
        ]);

        $receipt->delete();
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testSale && $this->testSale->id) {
            $this->testSale->delete();
        }
        if ($this->testProduct && $this->testProduct->id) {
            $this->testProduct->delete();
        }
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
