<?php

namespace app\controllers\api;

use Yii;
use app\models\Category;
use yii\data\ActiveDataProvider;
use yii\web\NotFoundHttpException;

/**
 * CategoryController implements the API endpoints for Category model
 */
class CategoryController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'index' => ['GET'],
            'view' => ['GET'],
            'create' => ['POST'],
            'update' => ['PUT', 'PATCH'],
            'delete' => ['DELETE'],
        ];
    }    /**
     * Lists all categories with filtering, sorting and pagination
     * @return array
     */
    public function actionIndex()
    {
        $query = Category::find();
        $request = Yii::$app->request;
        
        // Apply filters
        if ($search = $request->get('search')) {
            $query->andWhere(['or', 
                ['like', 'name', $search],
                ['like', 'description', $search]
            ]);
        }
        
        // Create data provider with default sorting by name
        $dataProvider = $this->createDataProvider($query, ['name' => SORT_ASC]);
        
        // Optionally eager load product counts
        if ($request->get('with_products_count', false)) {
            $models = $dataProvider->getModels();
            foreach ($models as $model) {
                $model->productsCount = $model->getProductsCount();
            }
            return $this->successResponse($models);
        }
        
        // Return paginated response
        return $this->paginatedResponse($dataProvider);
    }

    /**
     * Displays a specific category
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionView($id)
    {
        $model = $this->findModel($id);
        return $this->successResponse($model);
    }

    /**
     * Creates a new category
     * @return array
     */
    public function actionCreate()
    {
        $model = new Category();
        
        if ($model->load(Yii::$app->request->post(), '') && $model->save()) {
            return $this->successResponse($model, 201);
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Updates an existing category
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionUpdate($id)
    {
        $model = $this->findModel($id);
        
        if ($model->load(Yii::$app->request->post(), '') && $model->save()) {
            return $this->successResponse($model);
        }
        
        return $this->errorResponse('Ошибка валидации', 400, $model->getErrors());
    }

    /**
     * Deletes a category
     * @param int $id
     * @return array
     * @throws NotFoundHttpException if the model cannot be found
     */
    public function actionDelete($id)
    {
        $model = $this->findModel($id);
        
        if ($model->getProductsCount() > 0) {
            return $this->errorResponse('Нельзя удалить категорию, в которой есть товары', 400);
        }
        
        if ($model->delete()) {
            return $this->successResponse(null, 204);
        }
        
        return $this->errorResponse('Не удалось удалить категорию', 500);
    }

    /**
     * Finds the Category model based on its primary key value.
     * If the model is not found, a 404 HTTP exception will be thrown.
     * @param int $id
     * @return Category the loaded model
     * @throws NotFoundHttpException if the model cannot be found
     */
    protected function findModel($id)
    {
        if (($model = Category::findOne($id)) !== null) {
            return $model;
        }

        throw new NotFoundHttpException('Категория не найдена');
    }
}
