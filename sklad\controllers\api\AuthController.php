<?php

namespace app\controllers\api;

use Yii;
use app\models\User;
use app\models\LoginForm;
use app\components\JwtHelper;
use yii\web\UnauthorizedHttpException;

/**
 * AuthController implements authentication endpoints for API
 */
class AuthController extends BaseController
{
    /**
     * @inheritdoc
     */
    protected function verbs()
    {
        return [
            'login' => ['POST'],
            'verify' => ['POST'],
        ];
    }

    /**
     * Login action - validates credentials and returns JWT token
     * @return array
     */
    public function actionLogin()
    {
        $model = new LoginForm();
        
        if ($model->load(Yii::$app->request->post(), '') && $model->login()) {
            $user = $model->getUser();
            
            $token = JwtHelper::generateToken($user);
            
            return $this->successResponse([
                'token' => $token,
                'user' => [
                    'id' => $user->id,
                    'username' => $user->username,
                    'email' => $user->email,
                    'role' => $user->role,
                ],
            ]);
        }
        
        return $this->errorResponse('Неверное имя пользователя или пароль', 401, $model->getErrors());
    }

    /**
     * Verify JWT token validity
     * @return array
     */
    public function actionVerify()
    {
        $request = Yii::$app->request;
        $token = $request->post('token');
        
        if (empty($token)) {
            return $this->errorResponse('Токен не предоставлен', 400);
        }
        
        $payload = JwtHelper::parseToken($token);
        
        if ($payload === null) {
            return $this->errorResponse('Недействительный или просроченный токен', 401);
        }
        
        $user = User::findIdentity($payload['uid']);
        
        if (!$user) {
            return $this->errorResponse('Пользователь не найден', 401);
        }
        
        return $this->successResponse([
            'valid' => true,
            'user' => [
                'id' => $user->id,
                'username' => $user->username,
                'email' => $user->email,
                'role' => $user->role,
            ],
            'expires' => date('Y-m-d H:i:s', $payload['exp']),
        ]);
    }
}
