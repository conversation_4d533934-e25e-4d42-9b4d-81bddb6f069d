2025-06-03 17:09:49 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "api". in D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api', Array)
#1 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 D:\OSPanel\domains\warehouse\sklad\web\index.php(12): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\warehouse\sklad\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 D:\OSPanel\domains\warehouse\sklad\web\index.php(12): yii\base\Application->run()
#2 {main}
2025-06-03 17:09:49 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\sklad\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '59345'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\sklad\\web\\index.php'
    'PATH_INFO' => '/api'
    'PHP_SELF' => '/index.php/api'
    'HTTP_USER_AGENT' => 'Mozilla/5.0 (Windows NT; Windows NT 10.0; ru-RU) WindowsPowerShell/5.1.26100.4061'
    'HTTP_HOST' => 'localhost:8080'
    'HTTP_CONNECTION' => 'Keep-Alive'
    'REQUEST_TIME_FLOAT' => 1748970589.3646
    'REQUEST_TIME' => 1748970589
]
2025-06-03 17:20:29 [-][-][-][error][yii\console\Exception] yii\console\Exception: Unknown option: --limit. Options available: --color, --interactive, --help, --silentExitOnException, --migrationPath, --migrationNamespaces, --compact, --migrationTable, --db in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Controller.php:171
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php(552): yii\console\Controller->runAction('history', Array)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(180): yii\base\Module->runAction('migrate/history', Array)
#2 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\console\Application.php(147): yii\console\Application->runAction('migrate/history', Array)
#3 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\console\Application->handleRequest(Object(yii\console\Request))
#4 D:\OSPanel\domains\warehouse\yii(20): yii\base\Application->run()
#5 {main}
2025-06-03 17:20:29 [-][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'ALLUSERSPROFILE' => 'C:\\ProgramData'
    'APPDATA' => 'C:\\Users\\<USER>\\AppData\\Roaming'
    'c++' => 'C:\\MinGW\\bin'
    'ChocolateyInstall' => 'C:\\ProgramData\\chocolatey'
    'ChocolateyLastPathUpdate' => '133728599031302925'
    'CHROME_CRASHPAD_PIPE_NAME' => '\\\\.\\pipe\\crashpad_18696_YVZUWBOBGBZSUQCK'
    'CommonProgramFiles' => 'C:\\Program Files\\Common Files'
    'CommonProgramFiles(x86)' => 'C:\\Program Files (x86)\\Common Files'
    'CommonProgramW6432' => 'C:\\Program Files\\Common Files'
    'COMPUTERNAME' => 'WIN-QPO53K8FFA4'
    'ComSpec' => 'C:\\WINDOWS\\system32\\cmd.exe'
    'CURSOR_TRACE_ID' => '504e2ac837ef4a2e8360486aadbe4d80'
    'DataGrip' => 'D:\\app\\DataGrip 2024.1.3\\bin;'
    'DriverData' => 'C:\\Windows\\System32\\Drivers\\DriverData'
    'EFC_10372_1592913036' => '1'
    'EFC_10372_2283032206' => '1'
    'FPS_BROWSER_APP_PROFILE_STRING' => 'Internet Explorer'
    'FPS_BROWSER_USER_PROFILE_STRING' => 'Default'
    'git' => 'D:\\OSPanel\\Git\\bin'
    'HOMEDRIVE' => 'C:'
    'HOMEPATH' => '\\Users\\user'
    'LOCALAPPDATA' => 'C:\\Users\\<USER>\\AppData\\Local'
    'LOGONSERVER' => '\\\\WIN-QPO53K8FFA4'
    'nodejs' => 'C:\\Program Files\\nodejs\\node_modules\\npm\\bin'
    'NUMBER_OF_PROCESSORS' => '12'
    'OneDrive' => 'C:\\Users\\<USER>\\OneDrive'
    'OneDriveConsumer' => 'C:\\Users\\<USER>\\OneDrive'
    'ORIGINAL_XDG_CURRENT_DESKTOP' => 'undefined'
    'OS' => 'Windows_NT'
    'Path' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Program Files\\PowerShell\\7-preview\\preview;C:\\Users\\<USER>\\.fly\\bin;;C:\\Program Files\\Docker\\Docker\\resources\\bin;c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Python312\\Scripts\\;C:\\Python312\\;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\dotnet\\;C:\\php;C:\\Program Files\\Docker\\Docker\\resources\\bin;C:\\ProgramData\\ComposerSetup\\bin;C:\\ProgramData\\ComposerSetup\\bin\\composer.bat;D:\\OSPanel\\Git\\cmd;C:\\Program Files\\nodejs\\;C:\\ProgramData\\chocolatey\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;D:\\app\\DataGrip 2024.1.3\\bin;D:\\php\\OpenSSL-Win64\\bin;D:\\ngrok;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Schniz.fnm_Microsoft.Winget.Source_8wekyb3d8bbwe;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Windsurf\\bin;C:;C:\\Program Files\\PostgreSQL\\16\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin;C:\\Users\\<USER>\\.lmstudio\\bin;D:\\chrome data\\Windsurf Next\\bin;C:\\Users\\<USER>\\.fly\\bin;C:\\Users\\<USER>\\AppData\\Local\\JetBrains\\Toolbox\\scripts;D:\\Microsoft VS Code\\bin'
    'PATHEXT' => '.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW;.CPL'
    'php' => 'C:\\php'
    'PhpStorm' => 'C:\\Program Files\\JetBrains\\PhpStorm 2024.1.1\\bin;'
    'POWERSHELL_DISTRIBUTION_CHANNEL' => 'MSI:Windows 10 Pro'
    'PROCESSOR_ARCHITECTURE' => 'AMD64'
    'PROCESSOR_IDENTIFIER' => 'Intel64 Family 6 Model 154 Stepping 4, GenuineIntel'
    'PROCESSOR_LEVEL' => '6'
    'PROCESSOR_REVISION' => '9a04'
    'ProgramData' => 'C:\\ProgramData'
    'ProgramFiles' => 'C:\\Program Files'
    'ProgramFiles(x86)' => 'C:\\Program Files (x86)'
    'ProgramW6432' => 'C:\\Program Files'
    'PSModulePath' => 'C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules'
    'PUBLIC' => 'C:\\Users\\<USER>\\WINDOWS'
    'TEMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'TMP' => 'C:\\Users\\<USER>\\AppData\\Local\\Temp'
    'USERDOMAIN' => 'WIN-QPO53K8FFA4'
    'USERDOMAIN_ROAMINGPROFILE' => 'WIN-QPO53K8FFA4'
    'USERNAME' => 'user'
    'USERPROFILE' => 'C:\\Users\\<USER>\\Program Files\\Oracle\\VirtualBox\\'
    'windir' => 'C:\\WINDOWS'
    'ZES_ENABLE_SYSMAN' => '1'
    'GIT_PAGER' => ''
    'TERM_PROGRAM' => 'vscode'
    'TERM_PROGRAM_VERSION' => '0.51.1'
    'LANG' => 'en_US.UTF-8'
    'COLORTERM' => 'truecolor'
    'GIT_ASKPASS' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass.sh'
    'VSCODE_GIT_ASKPASS_NODE' => 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\Cursor.exe'
    'VSCODE_GIT_ASKPASS_EXTRA_ARGS' => ''
    'VSCODE_GIT_ASKPASS_MAIN' => 'c:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\extensions\\git\\dist\\askpass-main.js'
    'VSCODE_GIT_IPC_HANDLE' => '\\\\.\\pipe\\vscode-git-3505b024ba-sock'
    'VSCODE_INJECTION' => '1'
    'VSCODE_NONCE' => 'a65ce192-5b9a-4968-8e3b-fdeab7de0bb4'
    'VSCODE_STABLE' => '1'
    'PHP_SELF' => 'yii'
    'SCRIPT_NAME' => 'yii'
    'SCRIPT_FILENAME' => 'yii'
    'PATH_TRANSLATED' => 'yii'
    'DOCUMENT_ROOT' => ''
    'REQUEST_TIME_FLOAT' => 1748971229.8365
    'REQUEST_TIME' => 1748971229
    'argv' => [
        0 => 'yii'
        1 => 'migrate/history'
        2 => '--limit=20'
    ]
    'argc' => 3
]
2025-06-03 17:40:12 [127.0.0.1][-][-][error][yii\web\HttpException:404] yii\base\InvalidRouteException: Unable to resolve the request "api". in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Module.php:561
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php(103): yii\base\Module->runAction('api', Array)
#1 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#2 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#3 {main}

Next yii\web\NotFoundHttpException: Page not found. in D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\web\Application.php:115
Stack trace:
#0 D:\OSPanel\domains\warehouse\vendor\yiisoft\yii2\base\Application.php(384): yii\web\Application->handleRequest(Object(yii\web\Request))
#1 D:\OSPanel\domains\warehouse\web\index.php(12): yii\base\Application->run()
#2 {main}
2025-06-03 17:40:12 [127.0.0.1][-][-][info][application] $_GET = []

$_POST = []

$_FILES = []

$_COOKIE = []

$_SERVER = [
    'DOCUMENT_ROOT' => 'D:\\OSPanel\\domains\\warehouse\\web'
    'REMOTE_ADDR' => '127.0.0.1'
    'REMOTE_PORT' => '60669'
    'SERVER_SOFTWARE' => 'PHP 8.1.31 Development Server'
    'SERVER_PROTOCOL' => 'HTTP/1.1'
    'SERVER_NAME' => 'localhost'
    'SERVER_PORT' => '8080'
    'REQUEST_URI' => '/api'
    'REQUEST_METHOD' => 'GET'
    'SCRIPT_NAME' => '/index.php'
    'SCRIPT_FILENAME' => 'D:\\OSPanel\\domains\\warehouse\\web\\index.php'
    'PATH_INFO' => '/api'
    'PHP_SELF' => '/index.php/api'
    'HTTP_HOST' => 'localhost:8080'
    'CONTENT_TYPE' => 'application/json'
    'HTTP_CONTENT_TYPE' => 'application/json'
    'HTTP_ACCEPT' => 'application/json'
    'REQUEST_TIME_FLOAT' => 1748972412.833
    'REQUEST_TIME' => 1748972412
]
