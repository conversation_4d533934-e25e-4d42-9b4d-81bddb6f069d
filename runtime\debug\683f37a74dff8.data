a:14:{s:6:"config";s:1021:"a:5:{s:10:"phpVersion";s:6:"8.1.31";s:10:"yiiVersion";s:6:"2.0.52";s:11:"application";a:8:{s:3:"yii";s:6:"2.0.52";s:4:"name";s:13:"Warehouse API";s:7:"version";s:3:"1.0";s:8:"language";s:5:"en-US";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:6:"8.1.31";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:3:{s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.26.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:7:"2.0.5.0";s:5:"alias";a:1:{s:10:"@yii/faker";s:58:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.7.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:56:"D:\OSPanel\domains\warehouse\vendor/yiisoft/yii2-gii/src";}}}}";s:3:"log";s:20086:"a:1:{s:8:"messages";a:20:{i:0;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.308781;i:4;a:0:{}i:5;i:2743712;}i:1;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.308801;i:4;a:0:{}i:5;i:2744888;}i:2;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.319005;i:4;a:0:{}i:5;i:3640296;}i:3;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.322097;i:4;a:0:{}i:5;i:4300120;}i:4;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.322397;i:4;a:0:{}i:5;i:4324952;}i:17;a:6:{i:0;s:37:"Route requested: 'api/product/delete'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.324471;i:4;a:0:{}i:5;i:4557776;}i:18;a:6:{i:0;s:32:"Route to run: api/product/delete";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.326342;i:4;a:0:{}i:5;i:4815640;}i:19;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.340401;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6291936;}i:22;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393761;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6496880;}i:25;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.428065;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6545320;}i:28;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=4) AND ("status"=1)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436067;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6843064;}i:31;a:6:{i:0;s:55:"User '4' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:**********.447218;i:4;a:1:{i:0;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:7254328;}i:32;a:6:{i:0;s:69:"Running action: app\controllers\api\ProductController::actionDelete()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:**********.447282;i:4;a:0:{}i:5;i:7253256;}i:33;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.447759;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7310808;}i:36;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456335;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7323232;}i:39;a:6:{i:0;s:38:"SELECT * FROM "product" WHERE "id"='6'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.46025;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7328528;}i:42;a:6:{i:0;s:34:"DELETE FROM "product" WHERE "id"=6";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.478301;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:122;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7366552;}i:45;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'audit_log'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502154;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7405272;}i:48;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='audit_log'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508988;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7417744;}i:51;a:6:{i:0;s:550:"INSERT INTO "audit_log" ("user_id", "action", "table_name", "record_id", "old_values", "new_values", "ip_address", "user_agent", "created_at") VALUES (4, 'DELETE', 'product', 6, '"{\"id\":6,\"name\":\"Updated Test Product\",\"barcode\":\"9999999999999\",\"category_id\":null,\"unit_type\":\"piece\",\"price_per_unit\":\"25.99\",\"current_stock\":\"10.000\",\"description\":null,\"is_template\":false,\"created_at\":\"2025-06-03 22:57:58\",\"updated_at\":\"2025-06-03 22:57:59\",\"version\":2}"'::jsonb, NULL, '127.0.0.1', NULL, '2025-06-03 17:57:59')";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.513305;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:45;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7465592;}}}";s:9:"profiling";s:36972:"a:3:{s:6:"memory";i:7867072;s:4:"time";d:0.22843003273010254;s:8:"messages";a:22:{i:20;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.34042;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6293816;}i:21;a:6:{i:0;s:66:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=sklad";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.392472;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6296560;}i:23;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393809;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6499208;}i:24;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426847;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6518744;}i:26;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.428094;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6547560;}i:27;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.431507;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6549872;}i:29;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=4) AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436116;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6846464;}i:30;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=4) AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443139;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6850160;}i:34;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.447805;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7312672;}i:35;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455389;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7334752;}i:37;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456399;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7325096;}i:38;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.459861;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7327920;}i:40;a:6:{i:0;s:38:"SELECT * FROM "product" WHERE "id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460276;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7331512;}i:41;a:6:{i:0;s:38:"SELECT * FROM "product" WHERE "id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.475958;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7334848;}i:43;a:6:{i:0;s:34:"DELETE FROM "product" WHERE "id"=6";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.478373;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:122;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7369144;}i:44;a:6:{i:0;s:34:"DELETE FROM "product" WHERE "id"=6";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.500049;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:122;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7370328;}i:46;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'audit_log'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502212;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7407512;}i:47;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'audit_log'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508443;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7426936;}i:49;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='audit_log'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509019;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7419984;}i:50;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='audit_log'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511979;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7423168;}i:52;a:6:{i:0;s:550:"INSERT INTO "audit_log" ("user_id", "action", "table_name", "record_id", "old_values", "new_values", "ip_address", "user_agent", "created_at") VALUES (4, 'DELETE', 'product', 6, '"{\"id\":6,\"name\":\"Updated Test Product\",\"barcode\":\"9999999999999\",\"category_id\":null,\"unit_type\":\"piece\",\"price_per_unit\":\"25.99\",\"current_stock\":\"10.000\",\"description\":null,\"is_template\":false,\"created_at\":\"2025-06-03 22:57:58\",\"updated_at\":\"2025-06-03 22:57:59\",\"version\":2}"'::jsonb, NULL, '127.0.0.1', NULL, '2025-06-03 17:57:59')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.51333;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:45;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7469640;}i:53;a:6:{i:0;s:550:"INSERT INTO "audit_log" ("user_id", "action", "table_name", "record_id", "old_values", "new_values", "ip_address", "user_agent", "created_at") VALUES (4, 'DELETE', 'product', 6, '"{\"id\":6,\"name\":\"Updated Test Product\",\"barcode\":\"9999999999999\",\"category_id\":null,\"unit_type\":\"piece\",\"price_per_unit\":\"25.99\",\"current_stock\":\"10.000\",\"description\":null,\"is_template\":false,\"created_at\":\"2025-06-03 22:57:58\",\"updated_at\":\"2025-06-03 22:57:59\",\"version\":2}"'::jsonb, NULL, '127.0.0.1', NULL, '2025-06-03 17:57:59')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.516825;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:45;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7471936;}}}";s:2:"db";s:35445:"a:1:{s:8:"messages";a:20:{i:23;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.393809;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6499208;}i:24;a:6:{i:0;s:2810:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'user'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.426847;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6518744;}i:26;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.428094;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6547560;}i:27;a:6:{i:0;s:872:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='user'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.431507;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6549872;}i:29;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=4) AND ("status"=1)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.436116;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6846464;}i:30;a:6:{i:0;s:52:"SELECT * FROM "user" WHERE ("id"=4) AND ("status"=1)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.443139;i:4;a:3:{i:0;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:83;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:44:"D:\OSPanel\domains\warehouse\models\User.php";s:4:"line";i:96;s:8:"function";s:12:"findIdentity";s:5:"class";s:15:"app\models\User";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:57:"D:\OSPanel\domains\warehouse\components\JwtAuthFilter.php";s:4:"line";i:45;s:8:"function";s:18:"loginByAccessToken";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:6850160;}i:34;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.447805;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7312672;}i:35;a:6:{i:0;s:2813:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'product'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.455389;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7334752;}i:37;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.456399;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7325096;}i:38;a:6:{i:0;s:875:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='product'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.459861;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7327920;}i:40;a:6:{i:0;s:38:"SELECT * FROM "product" WHERE "id"='6'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.460276;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7331512;}i:41;a:6:{i:0;s:38:"SELECT * FROM "product" WHERE "id"='6'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.475958;i:4;a:2:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:141;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:120;s:8:"function";s:9:"findModel";s:5:"class";s:37:"app\controllers\api\ProductController";s:4:"type";s:2:"->";}}i:5;i:7334848;}i:43;a:6:{i:0;s:34:"DELETE FROM "product" WHERE "id"=6";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.478373;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:122;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7369144;}i:44;a:6:{i:0;s:34:"DELETE FROM "product" WHERE "id"=6";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.500049;i:4;a:1:{i:0;a:5:{s:4:"file";s:66:"D:\OSPanel\domains\warehouse\controllers\api\ProductController.php";s:4:"line";i:122;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}}i:5;i:7370328;}i:46;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'audit_log'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.502212;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7407512;}i:47;a:6:{i:0;s:2815:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'audit_log'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.508443;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7426936;}i:49;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='audit_log'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:**********.509019;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7419984;}i:50;a:6:{i:0;s:877:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='audit_log'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:**********.511979;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:44;s:8:"function";s:6:"insert";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7423168;}i:52;a:6:{i:0;s:550:"INSERT INTO "audit_log" ("user_id", "action", "table_name", "record_id", "old_values", "new_values", "ip_address", "user_agent", "created_at") VALUES (4, 'DELETE', 'product', 6, '"{\"id\":6,\"name\":\"Updated Test Product\",\"barcode\":\"9999999999999\",\"category_id\":null,\"unit_type\":\"piece\",\"price_per_unit\":\"25.99\",\"current_stock\":\"10.000\",\"description\":null,\"is_template\":false,\"created_at\":\"2025-06-03 22:57:58\",\"updated_at\":\"2025-06-03 22:57:59\",\"version\":2}"'::jsonb, NULL, '127.0.0.1', NULL, '2025-06-03 17:57:59')";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.51333;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:45;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7469640;}i:53;a:6:{i:0;s:550:"INSERT INTO "audit_log" ("user_id", "action", "table_name", "record_id", "old_values", "new_values", "ip_address", "user_agent", "created_at") VALUES (4, 'DELETE', 'product', 6, '"{\"id\":6,\"name\":\"Updated Test Product\",\"barcode\":\"9999999999999\",\"category_id\":null,\"unit_type\":\"piece\",\"price_per_unit\":\"25.99\",\"current_stock\":\"10.000\",\"description\":null,\"is_template\":false,\"created_at\":\"2025-06-03 22:57:58\",\"updated_at\":\"2025-06-03 22:57:59\",\"version\":2}"'::jsonb, NULL, '127.0.0.1', NULL, '2025-06-03 17:57:59')";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:**********.516825;i:4;a:3:{i:0;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:45;s:8:"function";s:7:"execute";s:5:"class";s:14:"yii\db\Command";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:55:"D:\OSPanel\domains\warehouse\components\AuditLogger.php";s:4:"line";i:108;s:8:"function";s:3:"log";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}i:2;a:5:{s:4:"file";s:47:"D:\OSPanel\domains\warehouse\models\Product.php";s:4:"line";i:265;s:8:"function";s:9:"logDelete";s:5:"class";s:26:"app\components\AuditLogger";s:4:"type";s:2:"::";}}i:5;i:7471936;}}}";s:5:"event";s:3489:"a:20:{i:0;a:5:{s:4:"time";d:**********.324146;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.326445;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.335639;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:3;a:5:{s:4:"time";d:**********.392459;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:4;a:5:{s:4:"time";d:**********.444015;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:5;a:5:{s:4:"time";d:**********.444061;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:15:"app\models\User";}i:6;a:5:{s:4:"time";d:**********.444178;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:7;a:5:{s:4:"time";d:**********.447244;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:**********.447262;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\controllers\api\ProductController";}i:9;a:5:{s:4:"time";d:**********.44768;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:10;a:5:{s:4:"time";d:**********.477339;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"app\models\Product";}i:11;a:5:{s:4:"time";d:**********.477567;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"app\models\Product";}i:12;a:5:{s:4:"time";d:**********.478043;s:4:"name";s:12:"beforeDelete";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"app\models\Product";}i:13;a:5:{s:4:"time";d:**********.500839;s:4:"name";s:11:"afterDelete";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"app\models\Product";}i:14;a:5:{s:4:"time";d:**********.522087;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\controllers\api\ProductController";}i:15;a:5:{s:4:"time";d:**********.522633;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:16;a:5:{s:4:"time";d:**********.522645;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:17;a:5:{s:4:"time";d:**********.522652;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:18;a:5:{s:4:"time";d:**********.523508;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:19;a:5:{s:4:"time";d:**********.523576;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:90:"a:3:{s:5:"start";d:**********.29631;s:3:"end";d:**********.524905;s:6:"memory";i:7867072;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2426:"a:3:{s:8:"messages";a:12:{i:5;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324406;i:4;a:0:{}i:5;i:4548456;}i:6;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324417;i:4;a:0:{}i:5;i:4549208;}i:7;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324421;i:4;a:0:{}i:5;i:4549960;}i:8;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324425;i:4;a:0:{}i:5;i:4550712;}i:9;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324429;i:4;a:0:{}i:5;i:4551784;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:16:"GET api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324438;i:4;a:0:{}i:5;i:4552584;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:17:"POST api/products";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324442;i:4;a:0:{}i:5;i:4553384;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:25:"GET api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324445;i:4;a:0:{}i:5;i:4554192;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:25:"PUT api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324448;i:4;a:0:{}i:5;i:4555000;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:27:"PATCH api/products/<id:\d+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.32445;i:4;a:0:{}i:5;i:4555808;}i:15;a:6:{i:0;s:51:"Request parsed with URL rule: api/products/<id:\d+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.324462;i:4;a:0:{}i:5;i:4557464;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:28:"DELETE api/products/<id:\d+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.324465;i:4;a:0:{}i:5;i:4557824;}}s:5:"route";s:18:"api/product/delete";s:6:"action";s:53:"app\controllers\api\ProductController::actionDelete()";}";s:7:"request";s:2447:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:200;s:14:"requestHeaders";a:4:{s:4:"host";s:14:"localhost:8080";s:12:"content-type";s:16:"application/json";s:6:"accept";s:16:"application/json";s:13:"authorization";s:195:"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDg5NzM0NzYsImV4cCI6MTc0OTU3ODI3NiwidWlkIjo0LCJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZSI6ImFkbWluIn0._4ey6ByxWUXYa0usC4le8h_A4Tcvc9vvdg37YokFQ2E";}s:15:"responseHeaders";a:7:{s:12:"X-Powered-By";s:10:"PHP/8.1.31";s:32:"Access-Control-Allow-Credentials";s:4:"true";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"683f37a74dff8";s:16:"X-Debug-Duration";s:3:"228";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=683f37a74dff8";s:10:"Set-Cookie";s:204:"_csrf=e9a4bd82726a83e227558d466ea9ecd68f9453a41bc4385b0479181b56f87aaaa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22MUCGGt6uvApkn7xmD7kKI5_bkVk21nXc%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:18:"api/product/delete";s:6:"action";s:53:"app\controllers\api\ProductController::actionDelete()";s:12:"actionParams";a:1:{s:2:"id";s:1:"6";}s:7:"general";a:5:{s:6:"method";s:6:"DELETE";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:20:{s:13:"DOCUMENT_ROOT";s:32:"D:\OSPanel\domains\warehouse\web";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:11:"REMOTE_PORT";s:5:"61525";s:15:"SERVER_SOFTWARE";s:29:"PHP 8.1.31 Development Server";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:11:"SERVER_NAME";s:9:"localhost";s:11:"SERVER_PORT";s:4:"8080";s:11:"REQUEST_URI";s:15:"/api/products/6";s:14:"REQUEST_METHOD";s:6:"DELETE";s:11:"SCRIPT_NAME";s:10:"/index.php";s:15:"SCRIPT_FILENAME";s:42:"D:\OSPanel\domains\warehouse\web\index.php";s:9:"PATH_INFO";s:15:"/api/products/6";s:8:"PHP_SELF";s:25:"/index.php/api/products/6";s:9:"HTTP_HOST";s:14:"localhost:8080";s:12:"CONTENT_TYPE";s:16:"application/json";s:17:"HTTP_CONTENT_TYPE";s:16:"application/json";s:11:"HTTP_ACCEPT";s:16:"application/json";s:18:"HTTP_AUTHORIZATION";s:195:"Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDg5NzM0NzYsImV4cCI6MTc0OTU3ODI3NiwidWlkIjo0LCJ1c2VybmFtZSI6InRlc3R1c2VyIiwicm9sZSI6ImFkbWluIn0._4ey6ByxWUXYa0usC4le8h_A4Tcvc9vvdg37YokFQ2E";s:18:"REQUEST_TIME_FLOAT";d:**********.294255;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:1:{s:2:"id";s:1:"6";}s:4:"POST";a:0:{}s:6:"COOKIE";a:0:{}s:5:"FILES";a:0:{}s:7:"SESSION";a:0:{}}";s:4:"user";s:1212:"a:5:{s:2:"id";i:4;s:8:"identity";a:10:{s:2:"id";s:1:"4";s:8:"username";s:10:"'testuser'";s:5:"email";s:18:"'<EMAIL>'";s:13:"password_hash";s:62:"'$2y$13$/Rwhn432uv8dcP2UptJ6CuZbhhb.u./aGDIzCJJQ6P7TgEC3j4squ'";s:8:"auth_key";s:34:"'P0da2g3e0XpmSCIIwFn3MeR61zJnTCOr'";s:4:"role";s:7:"'admin'";s:6:"status";s:1:"1";s:10:"created_at";s:21:"'2025-06-03 22:57:55'";s:10:"updated_at";s:21:"'2025-06-03 22:57:55'";s:13:"last_login_at";s:4:"null";}s:10:"attributes";a:10:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:8:"Username";}i:2;a:2:{s:9:"attribute";s:5:"email";s:5:"label";s:5:"Email";}i:3;a:2:{s:9:"attribute";s:13:"password_hash";s:5:"label";s:13:"Password Hash";}i:4;a:2:{s:9:"attribute";s:8:"auth_key";s:5:"label";s:8:"Auth Key";}i:5;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:6;a:2:{s:9:"attribute";s:6:"status";s:5:"label";s:6:"Status";}i:7;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:8;a:2:{s:9:"attribute";s:10:"updated_at";s:5:"label";s:10:"Updated At";}i:9;a:2:{s:9:"attribute";s:13:"last_login_at";s:5:"label";s:13:"Last Login At";}}s:13:"rolesProvider";N;s:19:"permissionsProvider";N;}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"683f37a74dff8";s:3:"url";s:36:"http://localhost:8080/api/products/6";s:4:"ajax";i:0;s:6:"method";s:6:"DELETE";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.294255;s:10:"statusCode";i:200;s:8:"sqlCount";i:10;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:7867072;s:14:"processingTime";d:0.22843003273010254;}s:10:"exceptions";a:0:{}}