<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Category;
use app\components\JwtHelper;

class CategoryControllerCest
{
    private $authToken;
    private $testCategory;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->description = 'Test category description';
        $this->testCategory->save();
    }

    public function testGetCategoriesWithAuth(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data');
    }

    public function testGetCategoriesWithoutAuth(ApiTester $I)
    {
        $I->sendGET('/api/categories');
        
        $I->seeResponseCodeIs(401);
    }

    public function testGetSingleCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories/' . $this->testCategory->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Test Category',
                'description' => 'Test category description'
            ]
        ]);
    }

    public function testGetNonExistentCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories/99999');
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCreateCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/categories', [
            'name' => 'New Test Category',
            'description' => 'New category description'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'New Test Category',
                'description' => 'New category description'
            ]
        ]);
    }

    public function testCreateCategoryWithInvalidData(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/categories', [
            'name' => '', // Empty name
            'description' => 'Invalid category'
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCreateCategoryWithDuplicateName(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/categories', [
            'name' => 'Test Category', // Same as existing category
            'description' => 'Duplicate name category'
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testUpdateCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPUT('/api/categories/' . $this->testCategory->id, [
            'name' => 'Updated Test Category',
            'description' => 'Updated description'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Updated Test Category',
                'description' => 'Updated description'
            ]
        ]);
    }

    public function testUpdateNonExistentCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPUT('/api/categories/99999', [
            'name' => 'Updated Category'
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testPartialUpdateCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPATCH('/api/categories/' . $this->testCategory->id, [
            'description' => 'Partially updated description'
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Test Category', // Should remain unchanged
                'description' => 'Partially updated description'
            ]
        ]);
    }

    public function testDeleteCategory(ApiTester $I)
    {
        // Create a category to delete
        $categoryToDelete = new Category();
        $categoryToDelete->name = 'Category to Delete';
        $categoryToDelete->description = 'This category will be deleted';
        $categoryToDelete->save();

        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendDELETE('/api/categories/' . $categoryToDelete->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
    }

    public function testDeleteNonExistentCategory(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendDELETE('/api/categories/99999');
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCategoriesWithPagination(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories?page=1&per-page=5');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data');
    }

    public function testCategoriesWithSearch(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories?search=Test');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true
        ]);
    }

    public function testCreateCategoryMinimalData(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/categories', [
            'name' => 'Minimal Category'
            // No description - should be optional
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'name' => 'Minimal Category'
            ]
        ]);
    }

    public function testUpdateCategoryWithInvalidData(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPUT('/api/categories/' . $this->testCategory->id, [
            'name' => '' // Empty name
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false
        ]);
    }

    public function testCategoryResponseStructure(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/categories/' . $this->testCategory->id);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseJsonMatchesJsonPath('$.success');
        $I->seeResponseJsonMatchesJsonPath('$.data.id');
        $I->seeResponseJsonMatchesJsonPath('$.data.name');
        $I->seeResponseJsonMatchesJsonPath('$.data.created_at');
        $I->seeResponseJsonMatchesJsonPath('$.data.updated_at');
        $I->seeResponseJsonMatchesJsonPath('$._meta.execution_time');
        $I->seeResponseJsonMatchesJsonPath('$._meta.performance_level');
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
