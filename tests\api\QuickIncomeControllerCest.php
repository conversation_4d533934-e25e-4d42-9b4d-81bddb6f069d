<?php

namespace tests\api;

use ApiTester;
use app\models\User;
use app\models\Product;
use app\models\Category;
use app\components\JwtHelper;

class QuickIncomeControllerCest
{
    private $authToken;
    private $testCategory;
    private $testProduct;

    public function _before(ApiTester $I)
    {
        // Create test user and get auth token
        $user = new User();
        $user->username = 'testuser';
        $user->email = '<EMAIL>';
        $user->setPassword('testpass123');
        $user->role = User::ROLE_ADMIN;
        $user->status = User::STATUS_ACTIVE;
        $user->save();
        
        $this->authToken = JwtHelper::generateToken($user);

        // Create test category
        $this->testCategory = new Category();
        $this->testCategory->name = 'Test Category';
        $this->testCategory->save();

        // Create test product
        $this->testProduct = new Product();
        $this->testProduct->name = 'Test Product';
        $this->testProduct->barcode = '1234567890123';
        $this->testProduct->category_id = $this->testCategory->id;
        $this->testProduct->unit_type = Product::UNIT_PIECE;
        $this->testProduct->price_per_unit = 25.99;
        $this->testProduct->current_stock = 10;
        $this->testProduct->save();
    }

    public function testScanBarcodeSuccess(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 5,
            'price_per_unit' => 30.00,
            'notes' => 'Test income'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'income' => [
                    'quantity' => 5,
                    'price_per_unit' => 30.00,
                    'notes' => 'Test income'
                ],
                'product' => [
                    'barcode' => '1234567890123',
                    'previous_stock' => 10,
                    'current_stock' => 15
                ]
            ]
        ]);
    }

    public function testScanBarcodeWithoutAuth(ApiTester $I)
    {
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(401);
    }

    public function testScanBarcodeInvalidBarcode(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => 'invalid',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Invalid barcode format'
            ]
        ]);
    }

    public function testScanBarcodeProductNotFound(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '9999999999999',
            'quantity' => 5,
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Product not found'
            ]
        ]);
    }

    public function testScanBarcodeInvalidQuantity(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/scan-barcode', [
            'barcode' => '1234567890123',
            'quantity' => -5, // Negative quantity
            'price_per_unit' => 30.00
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Quantity must be greater than 0'
            ]
        ]);
    }

    public function testQuickEntryWithExistingProduct(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/quick-entry', [
            'product' => [
                'id' => $this->testProduct->id
            ],
            'quantity' => 3,
            'price_per_unit' => 28.00,
            'notes' => 'Quick entry test'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'income' => [
                    'quantity' => 3,
                    'price_per_unit' => 28.00
                ],
                'was_created' => false
            ]
        ]);
    }

    public function testQuickEntryWithAutoCreate(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/quick-entry', [
            'product' => [
                'name' => 'New Auto Product',
                'barcode' => '7777777777777',
                'unit_type' => Product::UNIT_KG,
                'category_id' => $this->testCategory->id
            ],
            'quantity' => 10,
            'price_per_unit' => 15.50,
            'auto_create' => true,
            'notes' => 'Auto created product'
        ]);
        
        $I->seeResponseCodeIs(201);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'income' => [
                    'quantity' => 10,
                    'price_per_unit' => 15.50
                ],
                'product' => [
                    'name' => 'New Auto Product',
                    'barcode' => '7777777777777'
                ],
                'was_created' => true
            ]
        ]);
    }

    public function testQuickEntryProductNotFoundWithoutAutoCreate(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/quick-entry', [
            'product' => [
                'name' => 'Non Existent Product'
            ],
            'quantity' => 5,
            'price_per_unit' => 20.00,
            'auto_create' => false
        ]);
        
        $I->seeResponseCodeIs(404);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Product not found'
            ]
        ]);
    }

    public function testBatchIncomeSuccess(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/batch', [
            'items' => [
                [
                    'product_id' => $this->testProduct->id,
                    'quantity' => 3,
                    'price_per_unit' => 28.00,
                    'notes' => 'Batch item 1'
                ],
                [
                    'product_id' => $this->testProduct->id,
                    'quantity' => 2,
                    'price_per_unit' => 29.00,
                    'notes' => 'Batch item 2'
                ]
            ]
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'summary' => [
                    'total' => 2,
                    'success' => 2,
                    'errors' => 0
                ]
            ]
        ]);
    }

    public function testBatchIncomeWithErrors(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendPOST('/api/income/batch', [
            'items' => [
                [
                    'product_id' => $this->testProduct->id,
                    'quantity' => 3,
                    'price_per_unit' => 28.00,
                    'notes' => 'Valid item'
                ],
                [
                    'product_id' => 99999, // Non-existent product
                    'quantity' => 2,
                    'price_per_unit' => 29.00,
                    'notes' => 'Invalid item'
                ]
            ]
        ]);
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'summary' => [
                    'total' => 2,
                    'success' => 1,
                    'errors' => 1
                ]
            ]
        ]);
    }

    public function testBatchIncomeTooManyItems(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        
        // Create array with 101 items (over limit)
        $items = [];
        for ($i = 0; $i < 101; $i++) {
            $items[] = [
                'product_id' => $this->testProduct->id,
                'quantity' => 1,
                'price_per_unit' => 10.00
            ];
        }
        
        $I->sendPOST('/api/income/batch', [
            'items' => $items
        ]);
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Maximum 100 items allowed per batch'
            ]
        ]);
    }

    public function testSearchProducts(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/income/search?q=Test&limit=10');
        
        $I->seeResponseCodeIs(200);
        $I->seeResponseContainsJson([
            'success' => true,
            'data' => [
                'query' => 'Test'
            ]
        ]);
        $I->seeResponseJsonMatchesJsonPath('$.data.products');
    }

    public function testSearchProductsEmptyQuery(ApiTester $I)
    {
        $I->haveHttpHeader('Authorization', 'Bearer ' . $this->authToken);
        $I->sendGET('/api/income/search?q=');
        
        $I->seeResponseCodeIs(400);
        $I->seeResponseContainsJson([
            'success' => false,
            'error' => [
                'message' => 'Search query is required'
            ]
        ]);
    }

    public function _after(ApiTester $I)
    {
        // Cleanup test data
        if ($this->testProduct && $this->testProduct->id) {
            $this->testProduct->delete();
        }
        if ($this->testCategory && $this->testCategory->id) {
            $this->testCategory->delete();
        }
    }
}
